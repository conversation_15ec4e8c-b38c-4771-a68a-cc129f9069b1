export enum IdentifierType {
  LoadId = 'loadId',
  ThreadId = 'threadId',
}

export type Identifier = {
  type: IdentifierType;
  value: string;
};

export enum ChromeEvents {
  // Identifier parsing
  ParseGmailThreadId = 'ParseGmailThreadId',
  ParseNewAljexPro = 'ParseNewAljexPro',
  ParseTurvoPro = 'ParseTurvoPro',
  ParseRelayPro = 'ParseRelayPro',
  CheckProAvailable = 'CheckProAvailable',

  // Identifier updates
  UpdateTabWithIdentifier = 'UpdateTabWithIdentifier',
  ParsedIdentifierUpdated = 'ParsedIdentifierUpdated',
  IdentifierUpdatedFromContentScript = 'IdentifierUpdatedFromContentScript',

  // Clicks listeners
  DrumkitIconClicked = 'DrumkitIconClicked',

  // Cookies
  GetAuthCookie = 'GetAuthCookie',
  CookieUpdated = 'CookieUpdated',
  RemoveAuthCookie = 'RemoveAuthCookie',

  // Sidepanel
  OpenSidePanel = 'OpenSidePanel',
  CheckSidePanelOpen = 'CheckSidePanelOpen',
  SidePanelStateUpdated = 'SidePanelStateUpdated',

  // Misc
  GetCurrentTab = 'GetCurrentTab',
  CheckExtensionPin = 'CheckExtensionPin',
}

export enum MessageSources {
  GmailManualParse = 'GmailManualParse',
  NewAljexManualParse = 'NewAljexManualParse',
  TurvoManualParse = 'TurvoManualParse',
  RelayManualParse = 'RelayManualParse',
}
