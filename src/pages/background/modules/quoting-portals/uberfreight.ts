import * as Sentry from '@sentry/browser';

import { UberFreightSubmitAction } from '@alexandria/lib/hosts/quoting/uberfreight';

import {
  QuoteSidebarPortName,
  UpdateQuoteRequestDataAction,
} from 'constants/BackgroundScript';
import { CreateQuoteRequestSuggestionRequest } from 'lib/api/createQuoteRequestSuggestion';
import { PortalParseQuoteRequestResult } from 'types/ChromeScript';
import { Address, TransportType } from 'types/QuoteRequest';
import { Maybe, MaybeUndef, Undef } from 'types/UtilityTypes';
import {
  PortalActionResult,
  SubmitQuoteToPortalData,
} from 'types/chromescript/QuotingPortal';
import { SidepanelMessage } from 'types/chromescript/util';
import { QuotingPortals } from 'types/enums/Integrations';
import { SuggestionSourceCategories } from 'types/suggestions/QuoteSuggestions';

import { isSuppressedSidepanelError } from '../util';

let uberFreightParsedQuoteRequest: Maybe<
  Partial<CreateQuoteRequestSuggestionRequest>
> = null;

function isUberFreightUrl(urlStr: Undef<string>): boolean {
  try {
    const url = new URL(urlStr || '');
    const hostname = url.hostname?.toLowerCase();
    return (
      !!hostname &&
      (hostname.includes('uber.com') || hostname.includes('uberfreight.com')) &&
      !!url.pathname?.includes('/freight/carriers/fleet/')
    );
  } catch {
    return false;
  }
}

// When the sidepanel first connects, send the initial data to QuoteSidebar
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith(QuoteSidebarPortName)) {
    // Extract tab ID from port name (format: "QuoteSidebarPortName-{tabId}")
    const tabIdMatch = port.name.match(/-(\d+)$/);
    const targetTabId = tabIdMatch ? parseInt(tabIdMatch[1], 10) : undefined;

    if (uberFreightParsedQuoteRequest && targetTabId) {
      await chrome.runtime.sendMessage({
        action: UpdateQuoteRequestDataAction,
        data: uberFreightParsedQuoteRequest,
        targetTabId: targetTabId,
      } as SidepanelMessage);
    }
  }
});

// When user navigates to UberFreight tab, inject script to listen for load changes
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // IMPORTANT: Only process when the page is fully loaded
  if (changeInfo.status === 'complete') {
    if (isUberFreightUrl(changeInfo.url ?? tab.url)) {
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        args: [tabId],
        func: registerLoadChangeListener,
      });
    }
  }
});

/**
 * When user first navigates to UberFreight tab:
 * 1. Attach parseQuoteRequestData and tabId function to window
 * 2. Parse the initially displayed load data
 */
chrome.tabs.onUpdated.addListener(async (tabId, _, tab) => {
  try {
    if (!tab.url) return;
    if (!isUberFreightUrl(tab.url)) {
      return;
    }

    // Attach parsing function to window context for use by MutationObserver
    await chrome.scripting.executeScript({
      target: { tabId },
      args: [tabId],
      func: (tabId: number) => {
        const rowsSelector = 'tr.app-loadsTable-row';
        const detailSelectors = [
          '[data-testid="load-details"]',
          '[data-testid*="load-details"]',
          '[data-testid*="offer-details"]',
        ];

        window.__tabId = tabId;

        function sanitizeText(value: MaybeUndef<string>): string {
          return value ? value.replace(/\s+/g, ' ').trim() : '';
        }

        function parseLocationString(
          locationText: MaybeUndef<string>
        ): Partial<Address> {
          if (!locationText) return {};

          const cleaned = sanitizeText(locationText);
          const match = cleaned.match(/(.+?),\s*([A-Z]{2})\s*(\d{5})?/);

          if (match) {
            return {
              city: match[1].trim(),
              state: match[2],
              zip: match[3] || '',
            };
          }

          return { addressLine1: cleaned };
        }

        function parseDateTimeString(
          dateTimeText: MaybeUndef<string>
        ): Undef<Date> {
          if (!dateTimeText) return undefined;

          try {
            const match = dateTimeText.match(
              /([A-Za-z]{3})\s+(\d{1,2}),\s+(\d{1,2}:\d{2})/
            );
            if (!match) return undefined;

            const [, month, day, time] = match;
            const year = new Date().getFullYear();
            const date = new Date(`${month} ${day}, ${year} ${time}`);

            if (Number.isNaN(date.getTime())) return undefined;

            if (date.getTime() < Date.now()) {
              date.setFullYear(year + 1);
            }

            return date;
          } catch {
            return undefined;
          }
        }

        function mapEquipmentToTransportType(
          equipmentText: MaybeUndef<string>
        ): TransportType {
          if (!equipmentText) return 'VAN' as TransportType;

          const normalized = equipmentText.toLowerCase();

          if (/dry\s*van|^van$/.test(normalized)) return 'VAN' as TransportType;
          if (/reefer|refrigerated/.test(normalized))
            return 'REEFER' as TransportType;
          if (/flat\s*bed/.test(normalized)) return 'FLATBED' as TransportType;
          if (/hot\s*shot/.test(normalized)) return 'HOTSHOT' as TransportType;
          if (/box\s*truck/.test(normalized))
            return 'BOX TRUCK' as TransportType;

          return 'VAN' as TransportType;
        }

        function getFieldValue(
          container: MaybeUndef<Element>,
          labelText: string
        ): Maybe<string> {
          if (!container) return null;
          const labelEl = Array.from(
            container.querySelectorAll('[data-baseweb="typo-labelsmall"]')
          ).find((el) => sanitizeText(el.textContent) === labelText);
          return sanitizeText(
            labelEl?.parentElement?.querySelector(
              '[data-baseweb="typo-paragraphmedium"]'
            )?.textContent ?? ''
          );
        }

        function getFieldValueMulti(
          container: MaybeUndef<Element>,
          ...labels: string[]
        ): Maybe<string> {
          return (
            labels.map((l) => getFieldValue(container, l)).find((v) => v) ||
            null
          );
        }

        function getPageMode(): Maybe<'details' | 'list'> {
          if (detailSelectors.some((sel) => document.querySelector(sel))) {
            return 'details';
          }

          if (document.querySelector(rowsSelector)) {
            return 'list';
          }

          return null;
        }

        function getDetailRoot(): Maybe<Element> {
          for (const selector of detailSelectors) {
            const el = document.querySelector(selector);
            if (el) {
              return el;
            }
          }
          return null;
        }

        function parseDetailLoad(): PortalParseQuoteRequestResult {
          const detailRoot = getDetailRoot();
          if (!detailRoot) {
            return { data: null };
          }

          const facilityCards = detailRoot.querySelectorAll(
            '[data-testid="facility-card"]'
          );

          if (facilityCards.length < 2) {
            return { data: null };
          }

          const pickupCard = facilityCards[0];
          const deliveryCard = facilityCards[1];

          const pickupDate = parseDateTimeString(
            pickupCard
              .querySelectorAll('[data-baseweb="typo-paragraphmedium"]')[0]
              ?.textContent?.trim()
          );
          const pickupLocation = parseLocationString(
            [
              ...pickupCard.querySelectorAll(
                '[data-baseweb="flex-grid-item"] [data-baseweb="typo-paragraphmedium"]'
              ),
            ]
              .pop()
              ?.textContent?.trim() ?? ''
          );

          const deliveryDate = parseDateTimeString(
            deliveryCard
              .querySelectorAll('[data-baseweb="typo-paragraphmedium"]')[0]
              ?.textContent?.trim()
          );
          const deliveryLocation = parseLocationString(
            [
              ...deliveryCard.querySelectorAll(
                '[data-baseweb="flex-grid-item"] [data-baseweb="typo-paragraphmedium"]'
              ),
            ]
              .pop()
              ?.textContent?.trim() ?? ''
          );

          const bookCard = detailRoot.querySelector(
            '[data-testid="book-card"]'
          );

          // TODO: Capture both Auction ID and Load I (which later becomes `load.customerRef`) for win rate matching logic by customerRef.
          // For now we always capture Auction ID so we can correctly deduplicate quote requests on table vs. details pages.
          const loadId =
            getFieldValueMulti(
              bookCard || detailRoot,
              'Auction ID',
              'Auction Id'
            ) || '';
          const distanceText =
            getFieldValueMulti(
              bookCard || detailRoot,
              'Distance',
              'Est. distance'
            ) || '';
          const distanceMiles: Maybe<number> = distanceText
            ? parseFloat(distanceText.replace(/[^\d.]/g, '')) || null
            : null;

          const equipmentType =
            sanitizeText(
              detailRoot.querySelector(
                '[data-testid="equipment-card"] [data-baseweb="typo-headingxsmall"]'
              )?.textContent
            ) || 'Van';

          const transportType = mapEquipmentToTransportType(equipmentType);
          const htmlSnippet = detailRoot.innerHTML.substring(0, 5000);

          const customerName =
            sanitizeText(
              pickupCard.querySelector(
                '.css-mqlUy .css-jxqUxb, [data-baseweb="block"].css-mqlUy .css-jxqUxb'
              )?.textContent ?? ''
            ) || 'UberFreight';

          const res: CreateQuoteRequestSuggestionRequest = {
            customer: { name: customerName },
            customerExternalTMSID: '',
            transportType,
            pickupCity: pickupLocation.city || '',
            pickupState: pickupLocation.state || '',
            pickupZip: pickupLocation.zip || '',
            pickupDate: pickupDate ? pickupDate.toISOString() : null,
            deliveryCity: deliveryLocation.city || '',
            deliveryState: deliveryLocation.state || '',
            deliveryZip: deliveryLocation.zip || '',
            deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
            sourceCategory:
              'quoting-portal' as SuggestionSourceCategories.QuotingPortal,
            source: 'uberfreight' as QuotingPortals,
            sourceExternalID: loadId,
            sourceURL: window.location.href,
            htmlSnippet,
            fuelSurchargePerMile: null,
            fuelSurchargeTotal: null,
            distanceMiles,
          };

          return { data: res };
        }

        function getCell(
          row: HTMLTableRowElement,
          index: number
        ): Maybe<HTMLTableCellElement> {
          const cells = row.querySelectorAll<HTMLTableCellElement>('td');
          return cells.item(index) ?? null;
        }

        function getFirstAnchorHref(row: Element): string {
          const anchor = row.querySelector<HTMLAnchorElement>('a[href]');
          if (anchor?.href) {
            try {
              return new URL(anchor.href, window.location.href).href;
            } catch {
              return anchor.href;
            }
          }
          return window.location.href;
        }

        function parseListRow(
          row: HTMLTableRowElement
        ): PortalParseQuoteRequestResult {
          try {
            const pickupCell = getCell(row, 0);
            const distanceCell = getCell(row, 1);
            const deliveryCell = getCell(row, 2);
            const providerCell = getCell(row, 3);
            const equipmentCell = getCell(row, 4);
            const loadIdCell = getCell(row, 9);

            const pickupLocation = parseLocationString(
              sanitizeText(
                pickupCell?.querySelector('.ax-listItem-title')?.textContent ??
                  ''
              )
            );
            const pickupDate = parseDateTimeString(
              sanitizeText(
                pickupCell?.querySelector('.ax-listItem-caption')
                  ?.textContent ?? ''
              )
            );
            const deliveryLocation = parseLocationString(
              sanitizeText(
                deliveryCell?.querySelector('.ax-listItem-title')
                  ?.textContent ?? ''
              )
            );
            const deliveryDate = parseDateTimeString(
              sanitizeText(
                deliveryCell?.querySelector('.ax-listItem-caption')
                  ?.textContent ?? ''
              )
            );
            const providerName = sanitizeText(
              providerCell?.querySelector('.ax-listItem-title')?.textContent ??
                ''
            );
            const loadIdText = sanitizeText(
              loadIdCell?.querySelector('.ax-listItem-title')?.textContent ?? ''
            ).replace(/^#/, '');
            const distanceMiles: Maybe<number> = distanceCell
              ? parseFloat(
                  sanitizeText(distanceCell.textContent ?? '').replace(
                    /[^\d.]/g,
                    ''
                  )
                ) || null
              : null;
            const transportType = mapEquipmentToTransportType(
              sanitizeText(
                equipmentCell?.querySelector('.ax-listItem-title')
                  ?.textContent ?? ''
              )
            );

            const suggestion: CreateQuoteRequestSuggestionRequest = {
              customer: { name: providerName || 'UberFreight' },
              customerExternalTMSID: '',
              transportType,
              pickupCity: pickupLocation.city || '',
              pickupState: pickupLocation.state || '',
              pickupZip: pickupLocation.zip || '',
              pickupDate: pickupDate ? pickupDate.toISOString() : null,
              deliveryCity: deliveryLocation.city || '',
              deliveryState: deliveryLocation.state || '',
              deliveryZip: deliveryLocation.zip || '',
              deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
              fuelSurchargePerMile: null,
              fuelSurchargeTotal: null,
              distanceMiles,
              sourceCategory:
                'quoting-portal' as SuggestionSourceCategories.QuotingPortal,
              source: 'uberfreight' as QuotingPortals,
              // Annoyingly, load IDs are not consistent between the details view and the list view.
              // On table view it's the auction ID, on details view it's the actual load ID that I believe
              // becomes `load.customerRef` when the load is won. Whether or not the user quotes on table or details pages
              // will impact our ability to match won loads by customerRef.
              sourceExternalID: loadIdText,
              sourceURL: getFirstAnchorHref(row),
              htmlSnippet: row.outerHTML,
            };

            return { data: suggestion };
          } catch (error: any) {
            return {
              data: null,
              error: error?.message || new Error(error).message,
            };
          }
        }

        let selectedRowElement: Maybe<HTMLTableRowElement> = null;

        function getSelectedRow(): Maybe<HTMLTableRowElement> {
          // If we have a stored selection and it's still in DOM, use it
          if (
            selectedRowElement &&
            document.contains(selectedRowElement) &&
            selectedRowElement.matches(rowsSelector)
          ) {
            return selectedRowElement;
          }
          // Otherwise default to first row
          return document.querySelector<HTMLTableRowElement>(rowsSelector);
        }

        function selectRow(row: HTMLTableRowElement) {
          selectedRowElement = row;
        }

        function ensureListHandlers() {
          const rows = Array.from(
            document.querySelectorAll<HTMLTableRowElement>(rowsSelector)
          );
          if (rows.length === 0) {
            return;
          }

          rows.forEach((row) => {
            if ((row as any).__drumkitHandlersAttached) {
              return;
            }
            (row as any).__drumkitHandlersAttached = true;

            // Single click handler on the row
            row.addEventListener(
              'click',
              () => {
                selectRow(row);
                window.__notifyUberFreightChange?.();
              },
              true
            );

            // Handler for bid-related interactions
            const bidCell = row.querySelector('.search-table-bid-cell');
            bidCell
              ?.querySelectorAll<HTMLElement>('input, button, a')
              .forEach((el) => {
                if ((el as any).__drumkitHandlersAttached) return;
                (el as any).__drumkitHandlersAttached = true;

                (el instanceof HTMLInputElement
                  ? ['focus', 'input', 'change']
                  : ['click']
                ).forEach((evt) =>
                  el.addEventListener(
                    evt,
                    () => {
                      selectRow(row);
                      window.__notifyUberFreightChange?.();
                    },
                    true
                  )
                );
              });
          });

          // Set default selection if nothing selected
          if (!selectedRowElement || !document.contains(selectedRowElement)) {
            selectedRowElement = rows[0] || null;
          }
        }

        function parseListSelection(): PortalParseQuoteRequestResult {
          ensureListHandlers();
          const row = getSelectedRow();
          if (!row) {
            return { data: null };
          }
          return parseListRow(row);
        }

        window.__ensureUberFreightListHandlers = ensureListHandlers;

        window.parseQuoteRequestData = (): PortalParseQuoteRequestResult => {
          const mode = getPageMode();

          if (mode === 'details') {
            return parseDetailLoad();
          }

          if (mode === 'list') {
            return parseListSelection();
          }

          return { data: null };
        };
      },
    });

    // Parse initial load data
    const parseResult = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        try {
          return window.parseQuoteRequestData?.();
        } catch (error: any) {
          return {
            data: null,
            error: error?.message || new Error(error).message,
          };
        }
      },
    });

    const result = parseResult?.[0]
      ?.result as Undef<PortalParseQuoteRequestResult>;
    if (result?.error) {
      console.error(
        'Error parsing UberFreight quote request data: ',
        result.error
      );
      Sentry.captureException(
        'Error parsing UberFreight quote request data: ' + result.error
      );
      return;
    }

    // Store the parsed data for when sidepanel connects
    if (parseResult?.[0]?.result?.data) {
      uberFreightParsedQuoteRequest = parseResult[0].result.data;
    }

    // Send to sidepanel if it's already open
    if (parseResult?.[0]?.result?.data) {
      try {
        await chrome.runtime.sendMessage({
          action: UpdateQuoteRequestDataAction,
          data: parseResult[0].result.data,
          targetTabId: tabId,
        });
      } catch (error) {
        // "Receiving end does not exist error" expected if side panel isn't open yet
        if (
          error instanceof Error &&
          !error.message.includes('Receiving end does not exist')
        ) {
          Sentry.captureException('Error sending message to sidepanel' + error);
        }
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Known chrome/transient errors that are expected to happen - don't send to Sentry
    if (isSuppressedSidepanelError(errorMessage.toLowerCase())) {
      return;
    }

    Sentry.captureException(
      'Error parsing UberFreight quote request data: ' + errorMessage
    );
  }
});

/**
 * Registers a MutationObserver to detect when load data changes on the page
 */
function registerLoadChangeListener(_tabId: number) {
  if (window.__loadChangeListenerAdded) return;
  window.__loadChangeListenerAdded = true;
  window.__tabId = _tabId;

  const detailSelectors = [
    '[data-testid="load-details"]',
    '[data-testid*="load-details"]',
    '[data-testid*="offer-details"]',
  ];

  const callback: MutationCallback = function () {
    try {
      const hasDetail = detailSelectors.some(
        (selector) => document.querySelector(selector) !== null
      );
      const hasList = document.querySelector('tr.app-loadsTable-row') !== null;

      if (hasList) {
        window.__ensureUberFreightListHandlers?.();
      }

      const parseResult =
        (hasDetail || hasList) && window.parseQuoteRequestData
          ? (window.parseQuoteRequestData?.() as PortalParseQuoteRequestResult)
          : { data: null };

      if (parseResult?.error) {
        console.error(
          'Error parsing UberFreight quote request data:',
          parseResult.error
        );
        return;
      }

      chrome.runtime.sendMessage({
        action: 'updateQuoteRequestData',
        data: parseResult?.data ?? null,
        targetTabId: window.__tabId,
      } as SidepanelMessage);
    } catch (error) {
      console.error('UberFreight MutationObserver callback error:', error);
    }
  };

  const observer = new MutationObserver(callback);

  // Watch the main content area for changes
  const targetNode = document.querySelector('main') || document.body;

  if (targetNode) {
    observer.observe(targetNode, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['data-uber-selected', 'class'],
    });
  }

  window.__notifyUberFreightChange = () => callback([], observer);
  window.__ensureUberFreightListHandlers?.();

  // Initial check after a short delay to ensure DOM is ready
  setTimeout(() => callback([], observer), 250);
}

/**
 * Listen for messages to submit a quote to UberFreight.
 */
chrome.runtime.onMessage.addListener(
  (message: SidepanelMessage, _, sendResponse) => {
    if (message.action === UpdateQuoteRequestDataAction) {
      uberFreightParsedQuoteRequest =
        (message.data as Maybe<Partial<CreateQuoteRequestSuggestionRequest>>) ??
        null;
      return;
    }

    if (message.action === UberFreightSubmitAction) {
      handleUberFreightSubmit(message, sendResponse);
      return true;
    }
  }
);

async function handleUberFreightSubmit(
  message: SidepanelMessage,
  sendResponse: (response: any) => void
) {
  try {
    const targetTabId = message.targetTabId;
    if (targetTabId === undefined) {
      sendResponse({ success: false, error: 'No target tab ID provided.' });
      return;
    }

    const tab = await chrome.tabs.get(targetTabId as number);
    if (!tab?.id || !isUberFreightUrl(tab.url)) {
      sendResponse({ success: false, error: 'UberFreight tab not found.' });
      return;
    }

    // Inject script to fill and submit the quote form
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      args: [message.data as SubmitQuoteToPortalData],
      func: submitQuoteToUberFreight,
    });

    const scriptResult = result?.[0]?.result;
    if (scriptResult) {
      sendResponse(scriptResult);
    } else {
      sendResponse({
        success: false,
        error: 'Script executed but returned no result.',
      });
    }
  } catch (err: any) {
    sendResponse({ success: false, error: err?.message ?? String(err) });
  }
}

async function submitQuoteToUberFreight(
  _data: SubmitQuoteToPortalData
): Promise<PortalActionResult> {
  try {
    if (!_data?.flatRate) {
      return {
        success: false,
        error: 'No rate provided to input.',
      };
    }

    function getPageMode(): Maybe<'list' | 'detail'> {
      const detailSelectors = [
        '[data-testid="load-details"]',
        '[data-testid*="load-details"]',
        '[data-testid*="offer-details"]',
      ];

      if (detailSelectors.some((sel) => document.querySelector(sel))) {
        return 'detail';
      }

      if (document.querySelector('tr.app-loadsTable-row')) {
        return 'list';
      }

      return null;
    }

    async function handleDetailViewBidding(): Promise<PortalActionResult> {
      try {
        let modal = document.querySelector('[data-baseweb="modal"]');
        let bidInput: Maybe<HTMLInputElement> =
          modal?.querySelector('input[type="number"]') || null;

        if (!bidInput) {
          const bidButton = Array.from(
            document.querySelectorAll('button')
          ).find((btn) => btn.textContent?.toLowerCase().includes('bid'));

          if (!bidButton) {
            throw new Error('Bid button not found.');
          }

          (bidButton as HTMLElement).click();

          await new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 20;
            const interval = setInterval(() => {
              modal = document.querySelector('[data-baseweb="modal"]');
              bidInput =
                modal?.querySelector<HTMLInputElement>(
                  'input[type="number"]'
                ) || null;

              if (bidInput) {
                clearInterval(interval);
                resolve(bidInput);
              } else if (++attempts >= maxAttempts) {
                clearInterval(interval);
                reject(new Error('Bid modal did not appear.'));
              }
            }, 150);
          });
        }

        if (!bidInput) {
          throw new Error('Bid input field not found.');
        }

        const input = bidInput as HTMLInputElement;
        input.value = '';
        input.value = _data.flatRate.toString();

        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        if (!_data.isSubmitOnPortalEnabled) {
          return { success: true };
        }

        return {
          success: false,
          partialSuccess: true,
          error: 'Automatic submission is not supported yet.',
        };
      } catch (err: any) {
        return {
          success: false,
          error: err.message || String(err),
        };
      }
    }

    function handleListViewBidding(parsedData: any): PortalActionResult {
      try {
        function normalizeLoadId(value: MaybeUndef<string>): string {
          const sanitized = value ? value.replace(/\s+/g, ' ').trim() : '';
          return sanitized.replace(/^#/, '');
        }

        function findBidInputForListView(
          loadData: NonNullable<typeof parsedData>
        ): Maybe<HTMLInputElement> {
          const rows = Array.from(
            document.querySelectorAll<HTMLTableRowElement>(
              'tr.app-loadsTable-row'
            )
          );

          // Priority 1: Try to find row by Load ID (most reliable)
          const targetLoadId = normalizeLoadId(loadData.sourceExternalID ?? '');
          if (targetLoadId) {
            for (const row of rows) {
              const loadIdText = normalizeLoadId(
                row.querySelector('.app-load-id-cell')?.textContent ?? ''
              );
              if (loadIdText === targetLoadId) {
                const input = row.querySelector<HTMLInputElement>(
                  '.search-table-bid-cell input[type="number"]'
                );
                if (input) return input;
              }
            }
          }

          // Priority 2: Try to match by pickup and delivery locations
          if (loadData.pickupCity && loadData.deliveryCity) {
            const pickupCityLower = (loadData.pickupCity || '')
              .replace(/\s+/g, ' ')
              .trim()
              .toLowerCase();
            const deliveryCityLower = (loadData.deliveryCity || '')
              .replace(/\s+/g, ' ')
              .trim()
              .toLowerCase();

            for (const row of rows) {
              const pickupText = (
                row.querySelector('.app-loadsTable-cell:nth-child(1)')
                  ?.textContent ?? ''
              )
                .replace(/\s+/g, ' ')
                .trim()
                .toLowerCase();
              const deliveryText = (
                row.querySelector('.app-loadsTable-cell:nth-child(3)')
                  ?.textContent ?? ''
              )
                .replace(/\s+/g, ' ')
                .trim()
                .toLowerCase();

              if (
                pickupText.includes(pickupCityLower) &&
                deliveryText.includes(deliveryCityLower)
              ) {
                const input = row.querySelector<HTMLInputElement>(
                  '.search-table-bid-cell input[type="number"]'
                );
                if (input) return input;
              }
            }
          }

          return null;
        }

        const bidInput = findBidInputForListView(parsedData);

        if (!bidInput) {
          return {
            success: false,
            error: 'Unable to locate bid input for the selected load.',
          };
        }

        if (document.activeElement !== bidInput) {
          bidInput.focus({ preventScroll: true });
        }
        bidInput.value = String(_data.flatRate);
        bidInput.dispatchEvent(new Event('input', { bubbles: true }));
        bidInput.dispatchEvent(new Event('change', { bubbles: true }));

        (window as any).__notifyUberFreightChange?.();

        return { success: true };
      } catch (err: any) {
        return {
          success: false,
          error: err?.message || String(err),
        };
      }
    }

    const pageMode = getPageMode();

    if (pageMode === 'detail') {
      return await handleDetailViewBidding();
    }

    if (pageMode === 'list') {
      const parsedResult = (window as any).parseQuoteRequestData?.();
      const parsedData = parsedResult?.data;

      if (!parsedData) {
        return {
          success: false,
          error: 'Unable to determine the selected load to update.',
        };
      }

      return handleListViewBidding(parsedData);
    }

    return {
      success: false,
      error: 'Unable to determine page type (list or detail).',
    };
  } catch (err: any) {
    return {
      success: false,
      error: err?.message || String(err),
    };
  }
}
