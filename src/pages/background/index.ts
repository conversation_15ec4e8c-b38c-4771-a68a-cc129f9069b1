import {
  captureConsoleIntegration,
  captureException,
  getDefaultIntegrations,
  init,
} from '@sentry/browser';
import reloadOnUpdate from 'virtual:reload-on-update-in-background-script';

import APP_VERSION from '@constants/AppVersion';
import ENVIRONMENT from '@constants/Environment';
import SENTRY_DSN from '@constants/SentryDsn';
import { ChromeEvents, Identifier } from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

import {
  checkAuthCookies,
  cookieChangeListener,
  removeAuthCookies,
} from './auth/cookies';
import './modules/quoting-portals/e2open';
import './modules/quoting-portals/freightview';
import './modules/quoting-portals/shipwell';
import './modules/quoting-portals/uberfreight';
import { handleParseForAljex } from './modules/tms/aljex';
import { handleParseForNewAljex } from './modules/tms/new-aljex';
import {
  handleParseForRelay,
  registerRelayModalListener,
} from './modules/tms/relay';
import { handleParseForTai } from './modules/tms/tai';
import { handleParseForTurvo } from './modules/tms/turvo';
import './modules/util';
import { isSuppressedSidepanelError } from './modules/util';
import './sidepanel-opener/popup';
import { sendSidePanelMessage } from './sidepanel-opener/popup';

reloadOnUpdate('pages/background');

function initializeSentryForBackground() {
  init({
    dsn: SENTRY_DSN,
    environment: ENVIRONMENT,
    release: `${APP_VERSION}-${ENVIRONMENT}`,
    integrations: [
      captureConsoleIntegration({
        levels: ['error', 'warn'],
      }),
      ...getDefaultIntegrations({}),
    ],
    tracesSampleRate: 0.5,
  });
}

initializeSentryForBackground();

cookieChangeListener();

export const activeSidePanelTabs: Set<number> = new Set();

let parsedIdentifier: Maybe<Identifier> = null;
const pagesWithoutIdentifier: Set<number> = new Set(); // Track tabs without PRO IDs

// Listen to open sidepanel messages
chrome.runtime.onMessage.addListener(function (message, sender) {
  if (message.command === ChromeEvents.OpenSidePanel) {
    chrome.sidePanel.setOptions({
      tabId: sender.tab!.id,
    });
    chrome.sidePanel.open({
      tabId: sender.tab!.id,
      windowId: sender.tab!.windowId,
    });
  }

  // Handle events from Mutation Observer that watches for the Relay modals
  if (message.command === ChromeEvents.ParseRelayPro) {
    const tabId = message.tabId || sender.tab?.id;
    if (tabId) {
      handleParseForRelay(tabId);
    }
  }
  return true;
});

// When a sidepanel first connects, we parse the PRO ID from the Aljex tab (should work globally for all Aljex boards)
// and send the parsed ID to the sidepanel
// TODO (aaron): add PRO ID parsing for other TMS's here as well
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith('drumkitSidepanel')) {
    const tabId = parseInt(port.name.split('-')[1], 10);
    activeSidePanelTabs.add(tabId);

    try {
      const tab = await chrome.tabs.get(tabId);
      const href = tab.url ?? '';
      if (href.includes('aljex.com')) {
        await handleParseForAljex(tabId);
      } else if (href.includes('aljex.descartes.com')) {
        // New Aljex website
        await handleParseForNewAljex(tabId);
      } else if (href.includes('turvo.com')) {
        await handleParseForTurvo(tabId);
      } else if (href.includes('taicloud.net')) {
        await handleParseForTai(tabId, href);
      } else if (href.includes('relaytms.com')) {
        await handleParseForRelay(tabId);
      } else if (href.includes('mail.google.com')) {
        await handleParseForGmail(tabId);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error('error determining host on connect: ' + errorMessage)
        );
      }
    }

    chrome.runtime
      .sendMessage({
        action: ChromeEvents.ParsedIdentifierUpdated,
        parsedIdentifier,
        tabId: tabId,
      } as any)
      .catch((err) => console.error('Error sending initial parsed ID:', err));

    sendSidePanelMessage(true);

    port.onDisconnect.addListener(() => {
      activeSidePanelTabs.delete(tabId);
      sendSidePanelMessage(false);
    });
  }
});

/**
 * Extension reloading is necessary because the browser automatically caches the css.
 * If you do not use the css of the content script, please delete it.
 */

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'inboxsdk__injectPageWorld' && sender.tab) {
    if (chrome.scripting) {
      // MV3
      chrome.scripting.executeScript({
        target: { tabId: sender.tab.id! },
        world: 'MAIN',
        files: ['src/pages/pageWorld/index.js'],
      });
      sendResponse(true);
    } else {
      // MV2 fallback. Tell content script it needs to figure things out.
      sendResponse(false);
    }
    return true;
  }

  if (message.command === ChromeEvents.GetAuthCookie) {
    checkAuthCookies(sendResponse);
    return true;
  }

  if (message.command === ChromeEvents.RemoveAuthCookie) {
    removeAuthCookies(sendResponse);
    return true;
  }

  if (message.command === ChromeEvents.CheckProAvailable && sender.tab?.id) {
    const hasIdentifier = !pagesWithoutIdentifier.has(sender.tab.id);
    sendResponse({ hasIdentifier });
    return true;
  }

  if (
    message.command === ChromeEvents.IdentifierUpdatedFromContentScript &&
    sender.tab?.id
  ) {
    const tabId = sender.tab.id;
    const newParsedIdentifier = message.parsedIdentifier;

    applyParsedIdentifier(tabId, newParsedIdentifier);
    sendResponse({ success: true });
    return true;
  }
});

chrome.action.onClicked.addListener(async (tab) => {
  if (!tab || !tab.id) {
    return;
  }

  chrome.tabs
    .sendMessage(tab.id, {
      command: ChromeEvents.DrumkitIconClicked,
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error(
            'error sending drumkit action icon clicked message: ' + errorMessage
          )
        );
      }
    });
});

// Injects the Relay script into the tab when the user navigates to the planning board or hub
// This is necessary because Relay uses a modal to render the appointment forms, which is not
// rendered in the main DOM hence the need to inject a listening script to detect when the modal is opened.
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status !== 'complete') {
    return;
  }

  const urlStr = changeInfo.url ?? tab.url;
  if (!urlStr) {
    return;
  }

  const url = new URL(urlStr);

  // Relay script injection
  if (
    url.href.includes('relaytms.com/planning_board') ||
    url.href.includes('relaytms.com/hub')
  ) {
    chrome.scripting.executeScript({
      target: { tabId },
      func: registerRelayModalListener,
      args: [url.href, tabId],
    });
  }

  try {
    chrome.sidePanel.setOptions({
      tabId,
      path: 'src/pages/sidepanel/index.html',
      enabled: true,
    });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err);

    if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
      captureException(new Error('error enabling side panel: ' + errorMessage));
    }
  }

  if (url.origin.includes('aljex.com')) {
    await handleParseForAljex(tabId);
  } else if (url.origin.includes('turvo.com')) {
    await handleParseForTurvo(tabId);
  } else if (url.origin.includes('taicloud.net')) {
    await handleParseForTai(tabId, urlStr);
  } else if (url.origin.includes('relaytms.com')) {
    await handleParseForRelay(tabId);
  } else {
    pagesWithoutIdentifier.add(tabId);
  }
});

export function applyParsedIdentifier(
  tabId: number,
  identifier: Maybe<Identifier>
) {
  // Always trigger identifier update if different than last parsed identifier
  if (identifier && identifier.value != parsedIdentifier?.value) {
    chrome.runtime
      .sendMessage({
        action: ChromeEvents.ParsedIdentifierUpdated,
        parsedIdentifier: identifier,
        tabId: tabId,
      } as any)
      .catch(() => {});
  }

  // Update last parsed identifier
  parsedIdentifier = identifier;

  // Update hasIdentifier status
  chrome.tabs
    .sendMessage(tabId, {
      command: ChromeEvents.UpdateTabWithIdentifier,
      identifier: identifier,
    })
    .catch(() => {});

  // For Load IDs, continue existing logic
  if (!identifier) {
    pagesWithoutIdentifier.add(tabId);
  } else {
    pagesWithoutIdentifier.delete(tabId);
  }
}

// Parse the Gmail thread ID from the passed in Gmail tab
function handleParseForGmail(tabId: number) {
  chrome.tabs
    .sendMessage(tabId, {
      command: ChromeEvents.ParseGmailThreadId,
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error(
            'error sending parse gmail thread id message: ' + errorMessage
          )
        );
      }
    });
}

// Allows users to open the side panel on Aljex by clicking on the action toolbar icon
chrome.sidePanel
  .setPanelBehavior({ openPanelOnActionClick: true })
  .catch((error) => {
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
      captureException(
        new Error('error setting panel behavior: ' + errorMessage)
      );
    }
  });

chrome.runtime.onInstalled.addListener(async () => {
  chrome.contextMenus.create({
    id: 'openSidePanel',
    title: 'Open Drumkit',
    contexts: ['all'],
  });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'openSidePanel' && tab?.url) {
    chrome.sidePanel.open({ tabId: tab.id, windowId: tab.windowId });
  } else {
    // Disables the side panel on all other sites
    chrome.sidePanel.setOptions({
      tabId: tab?.id,
      enabled: false,
    });
  }
});
