# Adapted from: https://github.com/releasehub-com/github-action-create-pr-parent-submodule/
# Decided to use <PERSON>' action because 1) it offers more customization and 2) it's up-to-date. He's active in helping, both on the repo's Github page itself
# and on StackOverflow/other Github pages
# See ../workflows/auto-update-parent-repos.yml for usage
# See https://docs.github.com/en/actions/learn-github-actions/finding-and-customizing-actions for details on composite actions

name: 'GitHub Action - Auto-Update Alexandria Submodule'
description: 'Update submodules and creates new pull request against parent repository'

branding:
  icon: 'git-pull-request'
  color: 'red'

inputs:
  github_token:
    description: 'Github Token'
    required: true
  alexandria_pr_author:
    description: Github username of the author of the Alexandria PR that triggered the workflow
    required: true
  alexandria_pr_title:
    description: Title of the Alexandria PR that triggered the workflow
    required: true
  alexandria_branch:
    description: Name of the Alexandria branch that merged and triggered the workflow
    required: true
  parent_repository:
    description: 'Parent repository, must be in the form of {org}/{repo_name}, such as axleapi/mercury'
    required: true
  checkout_branch:
    description: 'Branch to checkout in parent repository. Generally the same as base_branch' # Set to dev branch for testing
    required: false
    default: 'main'
  base_branch:
    description: 'Branch in parent repo to set as the base for the PR' # Set to dev branch for testing
    required: false
    default: main
  labels:
    description: 'A comma or newline-separated list of labels.'
    required: false
    default: ''

outputs:
  status:
    description: "Status of parent repo's PR. Is either 'created' or 'branch already exists, skipped'. If the workflow itself errors out, that is not reflected in this field"
    value: ${{ steps.set_outputs.outputs.status }}
  pull-request-number:
    description: 'The pull request number'
    value: ${{ steps.set_outputs.outputs.pull-request-number }}
  pull-request-url:
    description: 'The URL of the generated pull request.'
    value: ${{ steps.set_outputs.outputs.pull-request-url }}

runs:
  using: composite
  steps:
    - name: Checkout parent repository and branch
      uses: actions/checkout@v3
      with:
        token: ${{ inputs.github_token }}
        repository: ${{ inputs.parent_repository }}
        ref: ${{ inputs.checkout_branch }}
        submodules: true
        fetch-depth: 0

    - name: Get list of branches in parent repo
      id: branch-list
      shell: bash
      run: |
        echo "list=$(git ls-remote origin ${{ inputs.alexandria_branch }} )" >> $GITHUB_OUTPUT

    - name: Check if branch already exists in parent
      id: branch-exists
      shell: bash
      run: echo "result=$( echo ${{ steps.branch-list.outputs.list }} | wc -w)" >> $GITHUB_OUTPUT

    # Action behavior is to force-push commit if branch/PR already exists, overriding all the other commits
    # So don't run this and next step if given branch already exists
    - name: Update Alexandria in parent
      if: steps.branch-exists.outputs.result == '0' # Conditional syntax: https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#jobsjob_idif
      shell: bash
      run: |
        git config user.name axle-github-bot
        git config user.email <EMAIL>
        git submodule update --remote

    - name: Setup Node and generate changeset
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        run: |
          yarn
          echo '---\n"drumkit": patch\n---\n\n${{ inputs.alexandria_pr_title }}\n' > $(yarn changeset --empty | grep 'info /' | awk '{print $3}')

    - name: Create Pull Request
      id: create_pr
      if: steps.branch-exists.outputs.result == '0'
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ inputs.github_token }}
        author: GitHub <<EMAIL>>
        branch: ${{ inputs.alexandria_branch }}
        commit-message: auto-update alexandria
        base: ${{ inputs.base_branch }}
        labels: ${{ inputs.labels }}
        title: Auto-update Alexandria from ${{ inputs.alexandria_branch }}
        body: |
          Auto-update Alexandria from ${{ inputs.alexandria_branch}}, authored by ${{ inputs.alexandria_pr_author }}
          
          **Alexandria PR Title:** ${{ inputs.alexandria_pr_title }}
        assignees: ${{ inputs.alexandria_pr_author }}

    # Re-declare nested outputs for local actions https://github.com/actions/runner/issues/2009#issuecomment-1199497525
    - name: Set outputs
      id: set_outputs
      shell: bash
      run: |
        if [[ "${{ steps.branch-exists.outputs.result }}" == "0" ]]; then
          echo "status=${{ steps.create_pr.outputs.pull-request-operation }}" >> $GITHUB_OUTPUT
          echo "pull-request-number=${{ steps.create_pr.outputs.pull-request-number }}" >> $GITHUB_OUTPUT
          echo "pull-request-url=${{ steps.create_pr.outputs.pull-request-url }}" >> $GITHUB_OUTPUT
        else
          echo "status=branch already exists, skipped" >> $GITHUB_OUTPUT;
        fi

    - name: Print outputs
      shell: bash
      run: |
        echo "OUTPUTS:"
        echo "status=${{ steps.set_outputs.outputs.status }}"
        echo "pull-request-number=${{ steps.set_outputs.outputs.pull-request-number }}"
        echo "pull-request-url=${{ steps.set_outputs.outputs.pull-request-url }}"
