import * as React from 'react';

import PublixLogoImg from '../assets/publix-logo.png';
import { cn } from '../utils/shadcn';

function PublixLogo(
  props: React.ImgHTMLAttributes<HTMLImageElement>
): React.JSX.Element {
  const { className: classNameProp, ...otherProps } = props;

  return (
    <img
      src={PublixLogoImg}
      alt='Publix logo'
      className={cn('filter brightness-75', classNameProp)}
      {...otherProps}
    />
  );
}

export default PublixLogo;
