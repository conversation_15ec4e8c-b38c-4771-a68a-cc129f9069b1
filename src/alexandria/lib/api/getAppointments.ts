import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type Appointment = {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  integrationId: number;
  freightTrackingId: string;
  loadId: number;
  tmsCustomerId: string | null;
  customerName: string;
  ExternalID: string;
  ConfirmationNo: string;
  ExternalPrivateID: number;
  ExternalWarehouseID: string;
  WarehouseID: number;
  LoadTypeID: string;
  DockID: string;
  Date: string;
  TimePreference: string;
  PONums: string;
  RefNumber: string;
  CcEmails: string[] | null;
  StartTime: string;
  Notes: string;
  Source: string;
  Status: string;
  EmailTemplateID: number;
  EmailBody: string;
  tenant: string;
};

export type GetAppointmentsResponse = {
  appointments: Appointment[];
};

export async function getAppointments(
  freightTrackingID: string
): Promise<Result<GetAppointmentsResponse, ApiError>> {
  try {
    const response = await axios.get<GetAppointmentsResponse>(
      `/appt?freightTrackingID=${encodeURIComponent(freightTrackingID)}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getAppointments' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get appointments' });
    }

    return err({
      message: error.response?.data.message || 'Failed to get appointments',
    });
  }
}
