import { QuotingPortal } from 'lib/hosts/quoting/interface';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export const UberFreightSubmitAction = 'uberfreight-submit-quote';

export class UberFreight implements QuotingPortal {
  submitAction = UberFreightSubmitAction;

  /**
   * @param tab
   * @param html
   * @returns
   */
  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean {
    if (!tab?.url) return false;
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    try {
      const url = new URL(tab.url);
      const isUberFreight =
        url.hostname?.includes('uber.com') &&
        url.pathname?.includes('/freight/carriers/fleet/');

      if (!isUberFreight) return false;

      const hasLoadDetails =
        doc.querySelector('[data-testid="load-details"]') !== null;
      const hasBookCard =
        doc.querySelector('[data-testid="book-card"]') !== null;

      const hasBidInput = Boolean(
        doc.querySelector<HTMLInputElement>(
          '.search-table-bid-cell input[type="number"]'
        )
      );

      const hasBidButton = Array.from(doc.querySelectorAll('button')).some(
        (btn) =>
          btn.textContent?.toLowerCase().includes('bid') ||
          btn.textContent?.toLowerCase().includes('quote')
      );

      const hasTableRow = Boolean(doc.querySelector('tr.app-loadsTable-row'));

      // Check for bid modal (either already open or can be opened)
      const hasBidModal = doc.querySelector('[data-baseweb="modal"]') !== null;
      const hasBidInputInModal = Boolean(
        doc.querySelector('[data-baseweb="modal"] input[type="number"]')
      );

      return (
        hasLoadDetails ||
        hasBookCard ||
        (hasTableRow && (hasBidButton || hasBidInput)) ||
        hasBidButton ||
        hasBidModal ||
        hasBidInputInModal
      );
    } catch (error: any) {
      captureException(
        'Error checking UberFreight if quote can be submitted:',
        error
      );
      return false;
    }
  }
}
