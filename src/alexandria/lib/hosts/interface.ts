import { AvailableTabs } from 'constants/SidebarTabs';
import { McleodEnterprise } from 'lib/hosts/mcleodenterprise';
import { QuantumEdge } from 'lib/hosts/quantumedge';
import { CustomHost, DrumkitHost } from 'types/DrumkitHosts';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import captureException from 'utils/captureException';

import { Aljex } from './aljex';
import { Gmail } from './gmail';
import { Relay } from './relay';
import { Revenova } from './revenova';
import { Tai } from './tai';
import { Turvo } from './turvo';

export interface HostInterface {
  /*
   * The name of the field to display in the load details sidebar. For example, for Relay ExternalTMSID is "Load #",
   * and FreightTrackingID is "Booking ID" and is the ID commonly shared in communications with carriers.
   */
  getFieldMoniker(
    key: keyof Pick<NormalizedLoad, 'externalTMSID' | 'freightTrackingID'>
  ): string;

  /**
   * Parse the freight tracking ID from the web page so Drumkit can display it in the sidebar.
   * Freight Tracking ID is the ID users commonly use to identify the load.
   * @returns The freight tracking ID
   */
  parseFreightTrackingID(): string;

  /**
   * Parse the external TMS ID from the web page so Drumkit can display it in the sidebar.
   * ExternalTMSID is the UUID of the load in the TMS. Sometimes this is the same as FreightTrackingID (Aljex, McleodEnterprise)
   * but sometimes it is not (Turvo Custom ID vs. UUID, Relay Load # vs. Booking ID).
   * In the cases where they are the same, parseExternalTMSID can simply return parseFreightTrackingID().
   */
  parseExternalTMSID(): string;

  /**
   * Determine the default tab to open in the load details sidebar. For example, on the Relay planning board
   * Drumkit automatically opens the appointment scheduling tab, but on the tracking board,
   * it opens the track & trace tab.
   */
  determineDefaultLoadTab(): AvailableTabs;

  /**
   * @deprecated now that we've switched to sidepanel which can never open on its own, user must always open the sidepanel
   * or already have it open
   * Determine if the Drumkit should automatically open when the load details sidebar is opened.
   */
  shouldAutomaticallyOpenDrumkit(): boolean;
}

export function createHostInstance(host: DrumkitHost): Maybe<HostInterface> {
  if (!host) return null;

  try {
    return hostToDrumkitHostInstanceMap[host];
  } catch (error: any) {
    captureException(error, { host, functionName: 'createHostInstance' });
    return null;
  }
}

// Record to enforce new TMS enums are handled
const hostToDrumkitHostInstanceMap: Record<
  NonNullable<DrumkitHost>,
  Maybe<HostInterface>
> = {
  [TMS.Aljex]: new Aljex(),
  [TMS.Turvo]: new Turvo(),
  [TMS.Tai]: new Tai(),
  [TMS.Relay]: new Relay(),
  [TMS.McleodEnterprise]: new McleodEnterprise(),
  [CustomHost.Gmail]: new Gmail(),
  [TMS.QuantumEdge]: new QuantumEdge(),
  [TMS.Revenova]: new Revenova(),

  // TODO
  [TMS.FreightFlow]: null,
  [TMS.Mcleod]: null,
  [TMS.GlobalTranzTMS]: null,
  [TMS.ThreeG]: null,
};

export function determineHostWebsite(): DrumkitHost {
  switch (true) {
    case location.href.includes('aljex.com'):
    case location.href.includes('aljex.descartes.com'):
      return TMS.Aljex;
    case location.href.includes('turvo.com'):
      return TMS.Turvo;
    case location.href.includes('taicloud.net'):
      return TMS.Tai;
    case location.href.includes('relaytms.com'):
      return TMS.Relay;
    // Revenova Salesforce
    case location.href.includes('lightning.force.com'):
      return TMS.Revenova;
    case location.href.includes('mail.google.com'):
      return CustomHost.Gmail;
    default:
      return null;
  }
}
