import { SchedulingPortals } from 'types/Appointment';

// Constants for appointment support by source and tenant
export const SUPPORTS_CANCEL_APPOINTMENT: string[] = [
  SchedulingPortals.C3Reservations,
  SchedulingPortals.Manhattan,
  SchedulingPortals.YardView,
];

export const SUPPORTS_RESCHEDULE_APPOINTMENT: string[] = [
  SchedulingPortals.C3Reservations,
  SchedulingPortals.Manhattan,
  SchedulingPortals.YardView,
];

export const C3_TENANT_SUPPORT_CANCEL_APPOINTMENT: string[] = [
  'dollartree',
];

export const C3_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT: string[] = [
  'dollartree',
];

export const MANHATTAN_TENANT_SUPPORT_CANCEL_APPOINTMENT: string[] = [
  'ahlod',
  'sysco',
];

export const MANHATTAN_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT: string[] = [
  'ahlod',
  'sysco',
];

// Helper functions to check if cancel/reschedule is supported
export const isCancelSupported = (source: string, tenant: string): boolean => {
  if (SUPPORTS_CANCEL_APPOINTMENT.includes(source)) {
    if (source === SchedulingPortals.C3Reservations) {
      return C3_TENANT_SUPPORT_CANCEL_APPOINTMENT.includes(tenant);
    }

    if (source === SchedulingPortals.Manhattan) {
      return MANHATTAN_TENANT_SUPPORT_CANCEL_APPOINTMENT.includes(tenant);
    }

    return true;
  }

  return false;
};

export const isRescheduleSupported = (source: string, tenant: string): boolean => {
  if (SUPPORTS_RESCHEDULE_APPOINTMENT.includes(source)) {
    if (source === SchedulingPortals.C3Reservations) {
      return C3_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT.includes(tenant);
    }

    if (source === SchedulingPortals.Manhattan) {
      return MANHATTAN_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT.includes(tenant);
    }
    return true;
  }
  
  return false;
};
