import { SchedulingPortals } from "types/Appointment";

// Constants for appointment support by source and tenant
export const SUPPORTS_CANCEL_APPOINTMENT = [
  SchedulingPortals.C3Reservations,
  SchedulingPortals.Manhattan,
];

export const SUPPORTS_RESCHEDULE_APPOINTMENT = [
  SchedulingPortals.C3Reservations,
  SchedulingPortals.Manhattan,
];

export const C3_TENANT_SUPPORT_CANCEL_APPOINTMENT = [
  'dollartree',
];

export const C3_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT = [
  'dollartree',
];

export const MANHATTAN_TENANT_SUPPORT_CANCEL_APPOINTMENT = [
  'ahlod',
  'sysco'
];

export const MANHATTAN_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT = [
  'ahlod',
  'sysco'
];

// Helper functions to check if cancel/reschedule is supported
export const isCancelSupported = (source: SchedulingPortals, tenant: string): boolean => {
  if (!SUPPORTS_CANCEL_APPOINTMENT.includes(source)) {
    return false;
  }

  if (source === SchedulingPortals.C3Reservations) {
    return C3_TENANT_SUPPORT_CANCEL_APPOINTMENT.includes(tenant);
  }

  if (source === SchedulingPortals.Manhattan) {
    return MANHATTAN_TENANT_SUPPORT_CANCEL_APPOINTMENT.includes(tenant);
  }

  return false;
};

export const isRescheduleSupported = (source: SchedulingPortals, tenant: string): boolean => {
  if (!SUPPORTS_RESCHEDULE_APPOINTMENT.includes(source)) {
    return false;
  }

  if (source === SchedulingPortals.C3Reservations) {
    return C3_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT.includes(tenant);
  }

  if (source === SchedulingPortals.Manhattan) {
    return MANHATTAN_TENANT_SUPPORT_RESCHEDULE_APPOINTMENT.includes(tenant);
  }

  return false;
};
