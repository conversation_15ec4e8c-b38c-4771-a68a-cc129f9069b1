import React from 'react';
import { useFormContext } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';

import AIHintLabel, { AIHintProps } from 'components/AIHint';
import RequiredHint from 'components/RequiredHint';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { cn } from 'utils/shadcn';

type FormInputWrapperProps = AIHintProps & {
  name: string;
  label: string;
  required?: boolean;
  description?: string;
  requiredIcon?: boolean;
  className?: string;
};

export default function FormInputWrapper({
  name,
  label,
  required,
  description,
  children,
  className,
  ...rest
}: React.PropsWithChildren<FormInputWrapperProps>) {
  const {
    formState: { errors },
  } = useFormContext();

  return (
    <label className='flex flex-col w-full'>
      <div className='mb-1'>
        <Flex
          direction='row'
          align='center'
          gap='xs'
          className={cn('text-sm text-neutral-800', className)}
        >
          <span className='wrap-break-word'>{label}</span>
          {required && <RequiredHint />}
          <AIHintLabel name={name} {...rest} />
        </Flex>
        {description && (
          <p className='text-xs text-neutral-500 wrap-break-word mt-1'>
            {description}
          </p>
        )}
      </div>
      {children}
      <ErrorMessage
        errors={errors}
        name={name}
        render={({ message }: { message: string }) => (
          <Typography variant='body-xs' className='text-error-500'>
            {message}
          </Typography>
        )}
      />
    </label>
  );
}
