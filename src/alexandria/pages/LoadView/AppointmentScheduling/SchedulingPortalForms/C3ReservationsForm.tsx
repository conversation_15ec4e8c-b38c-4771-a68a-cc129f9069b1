import { useContext, useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>ath,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { UpdateTMSLoadWithAppt } from 'components/UpdateTMSLoadWithAppt';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useToast } from 'hooks/useToaster';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { C3ReservationTypeNotice } from '../C3Reservations/C3ReservationTypeNotice';
import { C3UnsupportedSite } from '../C3Reservations/C3UnsupportedSite';
import {
  TenantFieldConfig,
  getReservationTypeOption,
  getTenantConfig,
  getTenantFieldConfig,
} from '../constants/c3ReservationScheduling';
import {
  extractTenantName,
  extractTenantSlug,
  formatTenantUrl,
} from '../helpers/c3ReservationHelpers';
import { confirmApptAndUpdateTMS } from '../helpers/confirmApptAndUpdateTMS';
import { isTMSUpdateLoadAppointmentSupported } from '../helpers/helpers';

dayjs.extend(utc);
dayjs.extend(timezone);

interface C3ReservationsInputsWithoutLoad {
  proId: string;
  startDate: Date;
  endDate: Date;
  reservationType: string;
  contactName: string;
  email: string;
  phone: string;
  unloadType: string;
  casesOrPalletQuantity: string;
  loadType: string;
  carrierCC: string;
  comment: string;
}

type C3ReservationsTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<C3ReservationsInputsWithoutLoad>;
};

const C3ReservationsTextInput = (props: C3ReservationsTextInputProps) => (
  <RHFTextInput {...props} />
);

interface C3ReservationsFormProps {
  type: StopTypes;
  load: NormalizedLoad;
  integrationID?: number;
  tmsName: string;
  isAppointmentTMSUpdateEnabled: boolean;
  tenant?: string;
  integrationNote?: string;
}

export function C3ReservationsForm({
  type,
  load,
  integrationID,
  tmsName,
  isAppointmentTMSUpdateEnabled,
  tenant,
  integrationNote,
}: C3ReservationsFormProps) {
  const {
    currentState: { openExternalUrl },
  } = useContext(SidebarStateContext);

  const { toast } = useToast();
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(false);
  const [showUpdateTMSLoadWithAppt, setShowUpdateTMSLoadWithAppt] =
    useState(false);

  const formMethods = useForm<C3ReservationsInputsWithoutLoad>({
    defaultValues: {
      proId: load.freightTrackingID || '',
      startDate: new Date(),
      endDate: dayjs().add(7, 'days').toDate(),
      reservationType: '',
      contactName: '',
      email: '',
      phone: '',
      unloadType: '',
      casesOrPalletQuantity: '',
      loadType: '',
      carrierCC: '',
      comment: '',
    },
    mode: 'onChange',
  });

  const { control, handleSubmit, getValues, setValue, watch } = formMethods;

  const unloadTypeValue = watch('unloadType');
  const loadTypeValue = watch('loadType');
  const reservationTypeValue = watch('reservationType');

  const resolvedTenant = extractTenantSlug(tenant || '');
  const tenantName = extractTenantName(tenant || '');
  const tenantConfig = getTenantConfig(resolvedTenant);
  const reservationTypeOption = getReservationTypeOption(
    tenantConfig,
    reservationTypeValue
  );
  const fieldConfig = getTenantFieldConfig(
    resolvedTenant,
    reservationTypeValue
  );
  const reservationTypeOptions = tenantConfig.reservationTypes ?? [];
  const tenantDisplayName =
    tenantName || tenantConfig.displayName || resolvedTenant || 'Tenant';
  const { display: formattedTenantUrl, href: formattedTenantHref } =
    formatTenantUrl(tenant);
  const hasTenantUrlLink =
    Boolean(formattedTenantUrl) && Boolean(formattedTenantHref);
  const handleTenantUrlClick = () => {
    if (!formattedTenantHref) return;
    openExternalUrl(formattedTenantHref);
  };
  const isLeclerc = tenantConfig.slug === 'leclerc';
  const hasSupportedReservationType =
    !!reservationTypeValue && !!reservationTypeOption?.supported;
  const requiresReservationType = !!tenantConfig.requiresReservationType;
  const isTenantSupported = tenantConfig.supported;
  const shouldRenderFormShell = isTenantSupported;
  const shouldShowReservationTypeSelect =
    shouldRenderFormShell && requiresReservationType;
  const canShowDetailFields =
    shouldRenderFormShell &&
    (!requiresReservationType || hasSupportedReservationType);
  const tenantUnsupportedMessage =
    !isTenantSupported && resolvedTenant ? 'Tenant Not Supported' : null;
  const reservationTypeUnsupportedMessage =
    isLeclerc && requiresReservationType && !hasSupportedReservationType
      ? 'Reservation Type Not Supported'
      : null;
  const submissionBlockedReason =
    tenantUnsupportedMessage || reservationTypeUnsupportedMessage;
  const shouldShowUnsupportedNotice = !!tenantUnsupportedMessage;
  const shouldShowReservationTypeUnsupportedNotice =
    shouldShowReservationTypeSelect && !!reservationTypeUnsupportedMessage;

  const previousTenantSlugRef = useRef<string>(extractTenantSlug(tenant || ''));
  const previousIntegrationRef = useRef<number | undefined>(integrationID);

  useEffect(() => {
    const currentTenantSlug = extractTenantSlug(tenant || '');
    const tenantChanged = currentTenantSlug !== previousTenantSlugRef.current;
    const integrationChanged = integrationID !== previousIntegrationRef.current;

    if (!tenantChanged && !integrationChanged) {
      return;
    }

    previousTenantSlugRef.current = currentTenantSlug;
    previousIntegrationRef.current = integrationID;

    // Clear tenant-specific fields so stale values don't carry over
    const currentValues = getValues();
    if (currentValues.unloadType) {
      setValue('unloadType', '');
    }
    if (currentValues.casesOrPalletQuantity) {
      setValue('casesOrPalletQuantity', '');
    }
    if (currentValues.loadType) {
      setValue('loadType', '');
    }
    if (currentValues.carrierCC) {
      setValue('carrierCC', '');
    }
    setValue('reservationType', '');

    // Clear cached slot data when switching integrations/tenants
    setOrderedSlots(null);
    setSelectedSlot(null);
    setApptConfirmationNumber('');
    setShowUpdateTMSLoadWithAppt(false);
  }, [tenant, integrationID, getValues, setValue]);

  /**
   * Build formData payload for API calls
   * Similar to cyclops/integrations/scheduling/c3reservations/models.py C3ReservationsFormData
   */
  const buildFormDataPayload = (
    values: C3ReservationsInputsWithoutLoad,
    options: {
      fieldConfig: TenantFieldConfig;
      tenantDisplayName: string;
      reservationType?: string;
    }
  ): { payload: Record<string, unknown>; error?: string } => {
    const { fieldConfig, tenantDisplayName, reservationType } = options;
    const fallbackName = tenantDisplayName || 'this tenant';
    const payload: Record<string, unknown> = {};

    // Contact fields
    if (fieldConfig.showFields.contactName) {
      const trimmed = values.contactName?.trim();
      if (fieldConfig.requiredFields.contactName && !trimmed) {
        return { payload: {}, error: 'Contact Name is required.' };
      }
      if (trimmed) {
        payload.contactName = trimmed;
      }
    }

    if (fieldConfig.showFields.email) {
      const trimmed = values.email?.trim();
      if (fieldConfig.requiredFields.email && !trimmed) {
        return { payload: {}, error: 'Email is required.' };
      }
      if (trimmed) {
        payload.email = trimmed;
      }
    }

    if (fieldConfig.showFields.phone && values.phone?.trim()) {
      payload.phone = values.phone.trim();
    }

    if (fieldConfig.showFields.unloadType) {
      if (fieldConfig.requiredFields.unloadType && !values.unloadType) {
        return {
          payload: {},
          error: `Unload Type is required for ${fallbackName}.`,
        };
      }
      if (values.unloadType) {
        payload.unloadType = values.unloadType;
      }
    }

    if (fieldConfig.showFields.casesOrPalletQuantity) {
      const qty = Number(values.casesOrPalletQuantity);
      if (
        fieldConfig.requiredFields.casesOrPalletQuantity &&
        (!values.casesOrPalletQuantity || Number.isNaN(qty) || qty <= 0)
      ) {
        return {
          payload: {},
          error: `Cases or Pallet Quantity is required for ${fallbackName}.`,
        };
      }
      if (values.casesOrPalletQuantity && !Number.isNaN(qty) && qty > 0) {
        payload.casesOrPalletQuantity = qty;
      }
    }

    if (fieldConfig.showFields.loadType) {
      if (fieldConfig.requiredFields.loadType && !values.loadType?.trim()) {
        return {
          payload: {},
          error: `Load Type is required for ${fallbackName}.`,
        };
      }
      if (values.loadType?.trim()) {
        payload.loadType = values.loadType.trim();
      }
    }

    if (fieldConfig.showFields.carrierCC && values.carrierCC?.trim()) {
      payload.carrierCC = values.carrierCC.trim();
    }

    if (fieldConfig.showFields.comment && values.comment?.trim()) {
      payload.comment = values.comment.trim();
    }

    if (reservationType) {
      payload.reservationType = reservationType;
    }

    return { payload };
  };

  const handleLoadAvailableSlots = async () => {
    const formValues = getValues();

    if (submissionBlockedReason) {
      toast({
        description: submissionBlockedReason,
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.proId?.trim()) {
      toast({
        description: 'Please enter a PRO / PO number.',
        variant: 'destructive',
      });
      return;
    }

    const start = dayjs(formValues.startDate);
    const end = dayjs(formValues.endDate);

    if (!start.isValid() || !end.isValid()) {
      toast({
        description: 'Please select a valid date range.',
        variant: 'destructive',
      });
      return;
    }

    if (end.isBefore(start)) {
      toast({
        description: 'End date must be after start date.',
        variant: 'destructive',
      });
      return;
    }

    const { payload: formDataPayload, error } = buildFormDataPayload(
      formValues,
      {
        fieldConfig,
        tenantDisplayName,
        reservationType: reservationTypeValue || undefined,
      }
    );
    if (error) {
      toast({
        description: error,
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingSlots(true);
    setSelectedSlot(null);

    try {
      setApptConfirmationNumber('');
      setShowUpdateTMSLoadWithAppt(false);

      const res = await getOpenApptSlots({
        source: SchedulingPortals.C3Reservations,
        loadTypeID: formValues.proId.trim(),
        freightTrackingID: load.freightTrackingID || '',
        warehouseTimezoneStartDate: start,
        warehouseTimezoneEndDate: end,
        integrationID,
        formData: formDataPayload,
        reservationType: reservationTypeValue || undefined,
      });

      if (res.isOk()) {
        const slots = res.value;
        setOrderedSlots(slots);

        if (!slots?.slots || Object.keys(slots.slots).length === 0) {
          toast({
            description:
              'No available slots were found for the selected dates.',
            variant: 'default',
          });
          return;
        }

        scrollResultsIntoViewRef.current?.scrollIntoView({
          behavior: 'smooth',
        });
        toast({
          description: 'Available slots retrieved successfully.',
          variant: 'success',
        });
      } else {
        toast({
          description:
            res.error.message || 'Failed to load available appointments.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description:
          'Please select a time slot before requesting an appointment.',
        variant: 'destructive',
      });
      return;
    }

    const formValues = getValues();

    if (submissionBlockedReason) {
      toast({
        description: submissionBlockedReason,
        variant: 'destructive',
      });
      return;
    }

    const { payload: formDataPayload, error } = buildFormDataPayload(
      formValues,
      {
        fieldConfig,
        tenantDisplayName,
        reservationType: reservationTypeValue || undefined,
      }
    );
    if (error) {
      toast({
        description: error,
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingConfirm(true);

    const warehouseTimezone =
      orderedSlots.warehouseTimezone || selectedSlot.timezone || 'UTC';

    const details: string[] = [];
    if (formValues.unloadType) {
      details.push(`Unload Type: ${formValues.unloadType}`);
    }
    if (formValues.casesOrPalletQuantity) {
      details.push(`Quantity: ${formValues.casesOrPalletQuantity}`);
    }
    if (formValues.loadType) {
      details.push(`Load Type: ${formValues.loadType}`);
    }
    if (formValues.carrierCC) {
      details.push(`Carrier CC: ${formValues.carrierCC}`);
    }
    if (formValues.comment) {
      details.push(`Comment: ${formValues.comment}`);
    }
    if (reservationTypeValue) {
      details.push(`Reservation Type: ${reservationTypeValue}`);
    }

    const notes =
      details.length > 0 ? `${details.join(' | ')}` : formValues.comment || '';

    try {
      const res = await confirmApptAndUpdateTMS({
        confirmApptProps: {
          source: SchedulingPortals.C3Reservations,
          isTMSLoad: false,
          stopType: type,
          start: selectedSlot.startTime,
          loadTypeId: formValues.proId.trim(),
          warehouseID:
            orderedSlots.warehouse?.warehouseID ||
            selectedSlot.dock.warehouseId ||
            'c3reservations',
          warehouseTimezone,
          dockId: selectedSlot.dock.id || 'c3reservations',
          loadID: load.ID!,
          freightTrackingId: load.freightTrackingID || '',
          notes,
          poNums: formValues.proId.trim(),
          email: formValues.email,
          phone: formValues.phone,
          formData: formDataPayload,
          reservationType: reservationTypeValue || undefined,
        },
        featureFlagEnabled: isAppointmentTMSUpdateEnabled,
        tmsName,
        timezone: warehouseTimezone,
        stopType: type,
        loadId: load.ID!,
        freightTrackingId: load.freightTrackingID || '',
        onTmsUpdateComplete: (succeeded, errorMessage) => {
          setTMSUpdateSucceeded(!!succeeded);
          if (succeeded) {
            toast({
              description: 'Load updated in TMS.',
              variant: 'success',
            });
          } else if (errorMessage) {
            toast({
              description: errorMessage,
              variant: 'destructive',
            });
          }
        },
      });

      if (res.confirmResult) {
        setApptConfirmationNumber(res.confirmResult.ConfirmationNo);
        if (
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
          isAppointmentTMSUpdateEnabled
        ) {
          toast({
            description:
              'Appointment requested. The TMS update will run in the background.',
            variant: 'default',
          });
        } else {
          toast({
            description: 'Appointment requested successfully!',
            variant: 'success',
          });
        }
        setShowUpdateTMSLoadWithAppt(
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
            !isAppointmentTMSUpdateEnabled
        );
      } else {
        const message =
          res.confirmError?.message || 'Failed to create appointment.';
        toast({
          description: message,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'An error occurred while creating the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  const onInvalid: SubmitErrorHandler<
    C3ReservationsInputsWithoutLoad
  > = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form
          onSubmit={handleSubmit(handleConfirmAppointment, onInvalid)}
          className='flex flex-col gap-4 mt-4 mx-0 w-full'
        >
          <div className='rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm'>
            <div className='mb-4'>
              <Typography variant='h5' className='mb-2' weight='medium'>
                Appointment Scheduling
              </Typography>
              {tenantName && (
                <Typography
                  variant='body-xs'
                  textColor='muted'
                  className='mt-1'
                >
                  Customer: {tenantName}
                </Typography>
              )}
              {integrationNote && (
                <Typography
                  variant='body-xs'
                  textColor='muted'
                  className='mt-1 break-all'
                >
                  Note: {integrationNote}
                </Typography>
              )}
            </div>

            {shouldShowUnsupportedNotice && (
              <div className='mb-4'>
                <C3UnsupportedSite
                  formattedTenantUrl={formattedTenantUrl}
                  hasTenantUrlLink={hasTenantUrlLink}
                  onOpenTenantUrl={handleTenantUrlClick}
                />
              </div>
            )}

            {shouldRenderFormShell && (
              <Flex direction='col' gap='md' className='w-full'>
                {/* PRO/PO Number */}
                <C3ReservationsTextInput
                  name='proId'
                  label='PRO / PO Number'
                  required
                />

                {/* Date Range */}
                <Flex direction='row' gap='md' wrap='wrap'>
                  <div className='flex-1 min-w-[220px]'>
                    <DateTimeInput
                      control={control}
                      name='startDate'
                      label='Start Date'
                      preventNormalizedLabelTZ={true}
                      hideAIHint={true}
                      hideTimePicker={true}
                    />
                  </div>
                  <div className='flex-1 min-w-[220px]'>
                    <DateTimeInput
                      control={control}
                      name='endDate'
                      label='End Date'
                      preventNormalizedLabelTZ={true}
                      hideAIHint={true}
                      hideTimePicker={true}
                    />
                  </div>
                </Flex>

                {shouldShowReservationTypeSelect && (
                  <Flex direction='row' gap='md' wrap='wrap' className='w-full'>
                    <div className='flex-1 min-w-[220px] flex flex-col gap-1'>
                      <Label htmlFor='reservationType' name='reservationType'>
                        Reservation Type
                        <span className='text-error-500'> *</span>
                      </Label>
                      <Select
                        value={reservationTypeValue}
                        onValueChange={(value: string) =>
                          setValue('reservationType', value, {
                            shouldDirty: true,
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select reservation type' />
                        </SelectTrigger>
                        <SelectContent>
                          {reservationTypeOptions.length === 0 ? (
                            <SelectItem value='' disabled>
                              No reservation types configured
                            </SelectItem>
                          ) : (
                            reservationTypeOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                                {!option.supported ? ' (Coming soon)' : ''}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </Flex>
                )}
                {shouldShowReservationTypeUnsupportedNotice &&
                  reservationTypeUnsupportedMessage && (
                    <C3ReservationTypeNotice
                      message={reservationTypeUnsupportedMessage}
                    />
                  )}

                {canShowDetailFields && (
                  <>
                    {/* Contact Information */}
                    {fieldConfig.showFields.contactName && (
                      <C3ReservationsTextInput
                        name='contactName'
                        label='Contact Name'
                        required={fieldConfig.requiredFields.contactName}
                      />
                    )}

                    {fieldConfig.showFields.email && (
                      <C3ReservationsTextInput
                        name='email'
                        label='Email'
                        required={fieldConfig.requiredFields.email}
                        placeholder='<EMAIL>'
                      />
                    )}

                    {fieldConfig.showFields.phone && (
                      <C3ReservationsTextInput
                        name='phone'
                        label='Phone#'
                        placeholder='(*************'
                      />
                    )}

                    {/* Dollar Tree Specific Fields */}
                    {fieldConfig.showFields.unloadType && (
                      <Flex direction='row' gap='md' wrap='wrap'>
                        <div className='flex-1 min-w-[220px]'>
                          <Label htmlFor='unloadType' name='unloadType'>
                            Unload Type
                            {fieldConfig.requiredFields.unloadType && (
                              <span className='text-error-500'> *</span>
                            )}
                          </Label>
                          <Select
                            value={unloadTypeValue}
                            onValueChange={(value: string) =>
                              setValue('unloadType', value, {
                                shouldDirty: true,
                              })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Select unload type' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Drop'>Drop</SelectItem>
                              <SelectItem value='Import'>Import</SelectItem>
                              <SelectItem value='Live'>Live</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {fieldConfig.showFields.casesOrPalletQuantity && (
                          <div className='flex-1 min-w-[220px]'>
                            <C3ReservationsTextInput
                              name='casesOrPalletQuantity'
                              label='Cases or Pallet Quantity (if pallets)'
                              inputType='number'
                              required={
                                fieldConfig.requiredFields.casesOrPalletQuantity
                              }
                            />
                          </div>
                        )}
                      </Flex>
                    )}

                    {/* KeHE Specific Fields */}
                    {fieldConfig.showFields.loadType && (
                      <Flex direction='row' gap='md' wrap='wrap'>
                        <div className='flex-1 min-w-[220px]'>
                          <Label htmlFor='loadType' name='loadType'>
                            Load Type
                            {fieldConfig.requiredFields.loadType && (
                              <span className='text-error-500'> *</span>
                            )}
                          </Label>
                          <Select
                            value={loadTypeValue}
                            onValueChange={(value: string) =>
                              setValue('loadType', value, { shouldDirty: true })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Select load type' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Cases Stacked on Floor'>
                                Cases Stacked on Floor
                              </SelectItem>
                              <SelectItem value='Cases Stacked on Pallets'>
                                Cases Stacked on Pallets
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        {fieldConfig.showFields.carrierCC && (
                          <div className='flex-1 min-w-[220px]'>
                            <C3ReservationsTextInput
                              name='carrierCC'
                              label='Carrier CC'
                            />
                          </div>
                        )}
                      </Flex>
                    )}

                    {/* Comment */}
                    {fieldConfig.showFields.comment && (
                      <C3ReservationsTextInput
                        name='comment'
                        label='Comment / Special Instructions'
                      />
                    )}

                    {/* Get Open Slots Button */}
                    <Button
                      type='button'
                      className='w-full'
                      disabled={isLoadingSlots}
                      buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                      onClick={handleLoadAvailableSlots}
                    >
                      {isLoadingSlots ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.GetOpenApptSlots
                      )}
                    </Button>
                  </>
                )}
              </Flex>
            )}
          </div>

          {/* Slots Display and Appointment Confirmation */}
          {orderedSlots && (
            <div
              className='rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm'
              ref={scrollResultsIntoViewRef}
            >
              <div className='mb-4'>
                <Typography variant='h5' className='mb-2' weight='medium'>
                  Available Slots
                </Typography>
              </div>

              {Object.entries(orderedSlots.slots).map(([date, slots]) => (
                <div key={date} className='mt-3'>
                  <Typography variant='body-sm' weight='medium'>
                    {date}
                  </Typography>
                  <Grid cols='3' gap='xs' className='mt-2 mx-0 w-full'>
                    {slots.map((slot, idx) => (
                      <button
                        type='button'
                        key={`${date}-${idx}`}
                        onClick={() =>
                          setSelectedSlot(selectedSlot === slot ? null : slot)
                        }
                        className={cn(
                          'text-neutral-900 bg-neutral-50 border border-neutral-400 p-2 rounded cursor-pointer text-sm',
                          selectedSlot === slot &&
                            'bg-brand border-brand-700 text-neutral-50'
                        )}
                      >
                        {dayjs(slot.startTime)
                          .tz(orderedSlots.warehouseTimezone || 'UTC')
                          .format('MMM D, HH:mm')}
                      </button>
                    ))}
                  </Grid>
                </div>
              ))}

              {selectedSlot && (
                <div className='mt-4 text-neutral-400 text-left text-sm'>
                  <Typography weight='bold' className='my-1'>
                    Selected Slot:
                  </Typography>
                  <Typography className='mb-2'>
                    {dayjs(selectedSlot.startTime)
                      .tz(orderedSlots.warehouseTimezone || 'UTC')
                      .format('MMM D, YYYY, HH:mm')}
                  </Typography>
                </div>
              )}

              {apptConfirmationNumber ? (
                <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                  <Typography className='mb-2'>
                    Appointment requested successfully! 🎉
                  </Typography>
                  <Typography variant='body-sm' className='mb-1'>
                    Confirmation #: {apptConfirmationNumber}
                  </Typography>
                  <Typography variant='body-sm'>
                    {isTMSUpdateLoadAppointmentSupported(tmsName) &&
                    isAppointmentTMSUpdateEnabled
                      ? tmsUpdateSucceeded
                        ? 'Your TMS was updated with the appointment details.'
                        : 'Your TMS update will run in the background.'
                      : 'Make sure to update your TMS with the scheduled appointment.'}
                  </Typography>
                </div>
              ) : (
                <Button
                  buttonNamePosthog={
                    ButtonNamePosthog.ConfirmSlotApptScheduling
                  }
                  className='mt-4 w-full'
                  type='submit'
                  disabled={
                    isLoadingConfirm ||
                    !selectedSlot ||
                    !!submissionBlockedReason
                  }
                >
                  {isLoadingConfirm ? <ButtonLoader /> : 'Request Appointment'}
                </Button>
              )}

              {apptConfirmationNumber &&
              showUpdateTMSLoadWithAppt &&
              selectedSlot ? (
                <UpdateTMSLoadWithAppt
                  load={load}
                  stopType={type}
                  appointmentStartTime={selectedSlot.startTime}
                  appointmentEndTime={
                    new Date(selectedSlot.startTime.getTime() + 60 * 60 * 1000)
                  }
                  freightTrackingId={load.freightTrackingID || ''}
                  onSuccess={() => setTMSUpdateSucceeded(true)}
                  onError={(error) =>
                    console.error('TMS update failed:', error)
                  }
                />
              ) : null}
            </div>
          )}
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
