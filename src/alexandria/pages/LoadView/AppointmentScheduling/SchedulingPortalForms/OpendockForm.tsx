import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
  Controller,
  FieldArrayWithId,
  FieldPath,
  FormProvider,
  SubmitHandler,
  useFieldArray,
  useForm,
} from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { ErrorMessage } from '@hookform/error-message';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore library installed on parent module, overriding tsc check
import { Select } from 'antd';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore library installed on parent module, overriding tsc check
import { BaseOptionType, SelectValue } from 'antd/es/select';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { LOAD_MONIKER } from '@constants/LoadMoniker';

import AttachmentUpload, {
  ProcessedAttachment,
} from 'components/AttachmentUpload';
import { Button } from 'components/Button';
import {
  DebounceSelect,
  GenericLocationOption,
} from 'components/DebounceSelect';
import { Label } from 'components/Label';
import { UpdateTMSLoadWithAppt } from 'components/UpdateTMSLoadWithAppt';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import SidebarLoader from 'components/loading/SidebarLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useAuth } from 'hooks/useAuth';
import { useToast } from 'hooks/useToaster';
import { getWarehouseData } from 'lib/api/getWarehouseData';
import {
  WarehouseLoadTypes,
  getWarehouseLoadTypes,
} from 'lib/api/getWarehouseLoadTypes';
import { getWarehouseSearch } from 'lib/api/getWarehouseSearch';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import {
  CustomApptFieldsTemplate,
  GetWarehouseResponse,
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
  WarehouseSettings,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import { LoadTypeDirections } from 'types/enums/LoadTypeDirection';
import { titleCase, toSentenceCase } from 'utils/formatStrings';

import TimeSlotButton from '../TimeSlotButton';
import WarehouseDisplay from '../WarehouseDisplay';
import { confirmApptAndUpdateTMS } from '../helpers/confirmApptAndUpdateTMS';
import {
  SevenElevenMarylandID,
  SevenElevenVirginiaID,
  aiSuggestCustomApptFields,
  isTMSUpdateLoadAppointmentSupported,
  removeSchedulerInfo,
  suggestRefNumberField,
} from '../helpers/helpers';

interface AppointmentFile {
  file: File;
  fileName: string;
  size: number;
}

dayjs.extend(utc);
dayjs.extend(timezone);

function convertInputToWarehouseTimezone(
  date: Date,
  warehouseTimezone: string
): dayjs.Dayjs {
  return dayjs()
    .tz(warehouseTimezone)
    .date(date.getDate())
    .month(date.getMonth())
    .year(date.getFullYear())
    .hour(date.getHours())
    .minute(date.getMinutes())
    .second(date.getSeconds())
    .millisecond(date.getMilliseconds());
}

type OpendockFormProps = {
  type: StopTypes;
  load: NormalizedLoad;
  integrationID?: number;
  tmsName: string;
  isAppointmentTMSUpdateEnabled: boolean;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

export function OpendockForm({
  type,
  load,
  integrationID,
  tmsName,
  isAppointmentTMSUpdateEnabled,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: OpendockFormProps) {
  const { toast } = useToast();
  const {
    currentState: { inboxEmailAddress },
  } = useContext(SidebarStateContext);
  const userEmail = useAuth().user?.email;

  const [loading, setLoading] = useState(false);
  const [suggestion, setSuggestion] =
    useState<Maybe<OpendockInputsWithoutLoad>>(null);

  const [selectedWarehouseDetails, setSelectedWarehouseDetails] =
    useState<GetWarehouseResponse>();
  const [warehouses, setWarehouses] = useState<Warehouse[]>(
    recentWarehouses && recentWarehouses.length ? recentWarehouses : []
  );
  const [uniqueWarehouseSources, setUniqueWarehouseSources] = useState<
    string[]
  >([]);
  const [isLoadingCustomFields, setIsLoadingCustomFields] = useState(false);
  const [isInitialSearch, setIsInitialSearch] = useState(true);
  const [hasOverriddenSuggestedWarehouse, setHasOverriddenSuggestedWarehouse] =
    useState(false);
  const [isSuggested, setIsSuggested] = useState(false);
  const [shortTZLabel, setShortTZLabel] = useState('');

  const [warehouseTimezone, setWarehouseTimezone] = useState<string>('');
  const [warehouseSettings, setWarehouseSettings] =
    useState<Maybe<WarehouseSettings>>(null);

  const [loadTypes, setLoadTypes] = useState<WarehouseLoadTypes[]>([]);
  const [selectedLoadType, setSelectedLoadType] =
    useState<WarehouseLoadTypes>();

  const [customApptFieldsTemplate, setCustomApptFieldsTemplate] = useState<
    CustomApptFieldsTemplate[] | null
  >(null);

  const [orderedSlots, setOrderedSlots] =
    useState<MaybeUndef<OrderedSlots>>(null);
  // To control scroll position when slots are rendered
  const slotsContainerRef = useRef<Maybe<HTMLDivElement>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(false);
  const [showUpdateTMSLoadWithAppt, setShowUpdateTMSLoadWithAppt] =
    useState(false);

  const [documentFiles, setDocumentFiles] = useState<
    Record<string, ProcessedAttachment[] | null>
  >({});

  useEffect(() => {
    if (warehouses) {
      setUniqueWarehouseSources([SchedulingPortals.Opendock]);
    }
  }, [warehouses]);

  // When warehouse selected, fetch its load types
  useEffect(() => {
    if (!selectedWarehouse) {
      return;
    }

    if (selectedWarehouse.warehouseSource === SchedulingPortals.Opendock) {
      handleFetchWarehouse();
    }
  }, [selectedWarehouse]);

  const handleFetchWarehouse = async () => {
    if (!selectedWarehouse) {
      return;
    }

    setIsLoadingCustomFields(true);

    const warehouseResponse = await getWarehouseData(
      selectedWarehouse.warehouseID,
      selectedWarehouse.warehouseSource,
      integrationID
    );
    if (warehouseResponse.isOk()) {
      setSelectedWarehouseDetails(warehouseResponse.value);
    } else {
      toast({
        description:
          warehouseResponse?.error?.message ??
          "Error fetching warehouse's custom form.",
        variant: 'destructive',
      });
    }

    setIsLoadingCustomFields(false);
  };

  const handleResetWarehouseSearch = () => {
    setIsInitialSearch(true);
    setWarehouses(recentWarehouses);
  };

  const handleWarehouseSearch = async (search: string) => {
    if (search.length > 3) {
      setIsInitialSearch(false);
      const searchRes = await getWarehouseSearch(search);
      if (searchRes.isOk()) {
        const { warehouses: searchedWarehouses } = searchRes.value ?? {
          warehouses: [],
        };
        // Prevent filtering on null/undefined result from API
        const safeSearchedWarehouses = Array.isArray(searchedWarehouses)
          ? searchedWarehouses
          : [];

        const opendockWarehouses = safeSearchedWarehouses.filter(
          (w) => w && w.warehouseSource === SchedulingPortals.Opendock
        );
        setWarehouses(opendockWarehouses);
        return opendockWarehouses && opendockWarehouses.length
          ? mapWarehousesToOptions(opendockWarehouses)
          : [];
      }
    }
    handleResetWarehouseSearch();
    return mapWarehousesToOptions(recentWarehouses).filter((wh) =>
      wh.label.toLocaleLowerCase().includes(search.toLocaleLowerCase())
    );
  };

  const mapWarehousesToOptions = (warehouses: Warehouse[]) =>
    warehouses?.map((option: Warehouse) => ({
      ...option,
      value: option.warehouseID,
      name: option.warehouseName,
      mainAddress: option.warehouseAddressLine1,
      secondaryAddress: option.warehouseAddressLine2,
      source: option.warehouseSource,
      label: option.warehouseName,
    }));

  useEffect(() => {
    if (!isSuggested) {
      return;
    }

    const sug = {
      warehouse: selectedWarehouse?.warehouseName,
      loadTypeID:
        selectedWarehouse?.warehouseID === SevenElevenMarylandID &&
        type === StopTypes.Dropoff
          ? '29f0b132-c23f-410d-9cab-1159f678c031' // Genesis Inbound
          : undefined,
    } as OpendockInputsWithoutLoad;

    setSuggestion(sug);
    setIsSuggested(true);
  }, [isSuggested]);

  // Some services want the TMS load ID included in the notes field. This function handles that logic.
  const aiSuggestNotes = (load: NormalizedLoad): string => {
    const stop = type === StopTypes.Dropoff ? load.consignee : load.pickup;

    // For NFI 7-11, include the load ID and the appt note in the notes field
    if (isSevenElevenWH(selectedWarehouse?.warehouseID ?? '')) {
      return `${LOAD_MONIKER} ${load.freightTrackingID}
          ${stop.apptNote ? `. ${removeSchedulerInfo(stop.apptNote)}` : ''}`;
    }

    // For Fetch Freight and others, always include the load ID in the notes field
    if (!inboxEmailAddress?.includes('nfiindustries.com')) {
      return `Load ID: ${load.freightTrackingID}`;
    }

    return '';
  };

  const memoizedDefaultValues: OpendockInputsWithoutLoad = useMemo(() => {
    const baseValues = {
      subscribedEmail: inboxEmailAddress,
      externalTMSID: load.externalTMSID,
      freightTrackingId: load.freightTrackingID,
      notes: aiSuggestNotes(load),
    } as OpendockInputsWithoutLoad;

    return (
      hasOverriddenSuggestedWarehouse
        ? baseValues
        : { ...baseValues, ...suggestion }
    ) as OpendockInputsWithoutLoad;
  }, [suggestion]);

  const startDateTime = new Date();
  startDateTime.setHours(0, 0, 0, 0);

  const endDateTime = new Date();
  endDateTime.setDate(endDateTime.getDate() + 7);
  endDateTime.setHours(23, 59, 59, 999);

  const formMethods = useForm<OpendockInputsWithoutLoad>({
    defaultValues: memoizedDefaultValues,
  });

  // Update form values when suggestion changes
  useEffect(() => {
    if (memoizedDefaultValues) {
      reset(memoizedDefaultValues);
      // Set outside of default values so as to not be displayed as AI-filled.
      formMethods.setValue('startDateTime', startDateTime);
      formMethods.setValue('endDateTime', endDateTime);
    }
  }, [memoizedDefaultValues]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    getValues,
    resetField,
    setError,
  } = formMethods;

  const { fields, update: updateCustomApptFields } = useFieldArray({
    control,
    name: 'customApptFieldsTemplate',
  });

  useEffect(() => {
    // resetting loadTypeID since it's a warehouse-dependent field.
    // we keep other form fields that could've been picked up by our parsing.
    resetField('loadTypeID', { defaultValue: '' });
    resetField('subscribedEmail', {
      defaultValue: selectedWarehouse?.defaultSubscribedEmail ?? userEmail,
    });

    // TODO: Move AI-filling logic to backend
    resetField('notes', { defaultValue: aiSuggestNotes(load) });
    // resetting dynamic fields whose logic we also control outside of form
    setWarehouseTimezone(selectedWarehouse?.warehouseTimezone ?? '');
    setCustomApptFieldsTemplate(null);
    setOrderedSlots(null);
    setLoadTypes([]);

    handleFetchLoadTypes();
  }, [selectedWarehouse]);

  // When warehouse's timezone changes, update datetime input displays
  useEffect(() => {
    // Using browser's native Intl library since dayjs didn't work with all timezones tested
    let shortTZ;
    if (warehouseTimezone) {
      shortTZ = new Intl.DateTimeFormat('en-US', {
        timeZone: warehouseTimezone,
        timeZoneName: 'short',
      })
        .format(Date.now())
        .split(' ')[1];
    } else {
      shortTZ = 'EDT';
    }

    setShortTZLabel(shortTZ);
  }, [warehouseTimezone]);

  // Display AI-filling for warehouse's custom fields
  useEffect(() => {
    fields.forEach((obj, index) => {
      resetField(`customApptFieldsTemplate.${index}.value`, {
        defaultValue: obj.value,
      });
    });
  }, [customApptFieldsTemplate]);

  // Scroll slots into view when they're rendered
  useEffect(() => {
    if (orderedSlots && Object.keys(orderedSlots.slots).length > 0) {
      slotsContainerRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [orderedSlots]);

  useEffect(() => {
    if (!selectedWarehouseDetails) {
      return;
    }

    const { settings, customApptFieldsTemplate, defaultSubscribedEmail } =
      selectedWarehouseDetails;

    setWarehouseSettings(settings);

    if (defaultSubscribedEmail) {
      formMethods.setValue('subscribedEmail', defaultSubscribedEmail);
    }

    // Ref Number is either PRO or PO, based on Opendock's description of the field
    const suggestedRef = suggestRefNumberField(
      settings,
      load.poNums ?? load.customer.refNumber,
      // QN does Relay use Relay # or Booking ID for appts? This assumes the former rn
      tmsName == TMS.Relay ? load.externalTMSID : load.freightTrackingID
    );
    resetField('opendockRefNumber', { defaultValue: suggestedRef });

    selectedWarehouse &&
      aiSuggestCustomApptFields(
        load,
        type,
        selectedWarehouse,
        customApptFieldsTemplate
      );

    setCustomApptFieldsTemplate(customApptFieldsTemplate);
    setValue('customApptFieldsTemplate', customApptFieldsTemplate);
  }, [selectedWarehouseDetails]);

  const onSubmitFetchSlots: SubmitHandler<OpendockInputsWithoutLoad> = async (
    data
  ) => {
    // Reset state variables when fetching slots
    setSelectedSlot(null);
    setApptConfirmationNumber('');
    setTMSUpdateSucceeded(false);
    setShowUpdateTMSLoadWithAppt(false);

    if (!data.loadTypeID) {
      toast({
        description: 'Please select a Load Type.',
        variant: 'default',
      });
      return;
    }

    setLoading(true);

    const warehouseTimezoneStartDate = convertInputToWarehouseTimezone(
      data.startDateTime,
      warehouseTimezone
    );
    const warehouseTimezoneEndDate = convertInputToWarehouseTimezone(
      data.endDateTime,
      warehouseTimezone
    );

    const res = await getOpenApptSlots({
      source: SchedulingPortals.Opendock,
      loadTypeID: data.loadTypeID,
      warehouseID: selectedWarehouse?.warehouseID ?? '',
      warehouseTimezoneStartDate,
      warehouseTimezoneEndDate,
      freightTrackingID: data.freightTrackingId,
      dockId: data.dockId,
      integrationID,
    });

    if (res.isOk()) {
      setOrderedSlots(res.value);

      const slots = res.value?.slots;
      if (!slots || Object.keys(slots).length === 0) {
        toast({
          description: 'No slots were found for the selected date range.',
          variant: 'default',
        });
      }
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    }

    setLoading(false);
  };

  const handleConfirmAppointment = async () => {
    setApptConfirmationNumber('');
    setTMSUpdateSucceeded(false);
    setShowUpdateTMSLoadWithAppt(false);

    if (!selectedSlot || !orderedSlots || !selectedWarehouse) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    setLoading(true);

    // collect all uploaded files from all document fields
    const allDocumentFiles: AppointmentFile[] = [];
    Object.values(documentFiles).forEach(
      (files: ProcessedAttachment[] | null) => {
        if (files && files.length > 0) {
          files.forEach((processedFile: ProcessedAttachment) => {
            allDocumentFiles.push({
              file: processedFile.originalFile,
              fileName: processedFile.fileName,
              size: processedFile.size,
            });
          });
        }
      }
    );

    const res = await confirmApptAndUpdateTMS({
      confirmApptProps: {
        source: SchedulingPortals.Opendock,
        isTMSLoad: true,
        stopType: type,
        start: selectedSlot.startTime,
        loadTypeId: orderedSlots.loadType.id,
        warehouseID: orderedSlots.warehouse.warehouseID,
        warehouseTimezone: selectedWarehouse.warehouseTimezone,
        dockId: selectedSlot.dock.id,
        loadID: load.ID!,
        freightTrackingId: getValues('freightTrackingId'),
        integrationID: integrationID,
        requestType: type,
        refNumber: getValues('opendockRefNumber'),
        trailerType: orderedSlots.trailerType,
        customApptFieldsTemplate: getValues('customApptFieldsTemplate'),
        subscribedEmail: getValues('subscribedEmail'),
        notes: getValues('notes'),
        documentFiles:
          allDocumentFiles.length > 0 ? allDocumentFiles : undefined,
      },
      featureFlagEnabled: isAppointmentTMSUpdateEnabled,
      tmsName,
      timezone: selectedSlot.timezone,
      stopType: type,
      loadId: load.ID!,
      freightTrackingId: getValues('freightTrackingId'),
      onTmsUpdateComplete: (succeeded, errorMessage) => {
        setTMSUpdateSucceeded(!!succeeded);
        if (succeeded) {
          toast({ description: 'Load updated in TMS.', variant: 'success' });
        } else if (errorMessage) {
          toast({ description: errorMessage, variant: 'destructive' });
        }
      },
    });

    if (res.confirmResult) {
      setApptConfirmationNumber(res.confirmResult.ConfirmationNo);
      if (
        isTMSUpdateLoadAppointmentSupported(tmsName) &&
        isAppointmentTMSUpdateEnabled
      ) {
        toast({
          description: 'Updating TMS in the background.',
          variant: 'default',
        });
      }
      // Show update TMS load with appointment form if TMS is supported and auto-update TMS is not enabled
      setShowUpdateTMSLoadWithAppt(
        isTMSUpdateLoadAppointmentSupported(tmsName) &&
          !isAppointmentTMSUpdateEnabled
      );
    } else {
      const message = res.confirmError?.message || 'Unable to book appointment';
      if (message === 'Conflicting Appointments') {
        toast({
          title: 'Conflicting Appointments',
          description:
            "Make sure you don't have an existing appointment for this load.",
          variant: 'destructive',
        });
      } else {
        toast({ description: message, variant: 'destructive' });
      }
    }

    setLoading(false);
  };

  const handleSwapPROAndBOL = (
    field: FieldArrayWithId<
      OpendockInputsWithoutLoad,
      'customApptFieldsTemplate',
      'id'
    >,
    newValue: string
  ) => {
    const fieldIndex = fields.findIndex((f) => f.id === field.id);

    updateCustomApptFields(fieldIndex, { ...field, value: newValue });
  };

  const handleFetchLoadTypes = async () => {
    if (!selectedWarehouse) {
      return;
    }

    const loadTypesResponse = await getWarehouseLoadTypes(
      selectedWarehouse.warehouseID,
      SchedulingPortals.Opendock,
      integrationID
    );
    if (loadTypesResponse.isOk()) {
      // filtering load types by inbound/outbound direction
      const directionLoadTypes = loadTypesResponse.value.loadTypes.filter(
        (lt) =>
          type === StopTypes.Dropoff
            ? lt.direction === LoadTypeDirections.Inbound
            : lt.direction === LoadTypeDirections.Outbound
      );

      // removing dock-selectable load types that have no docks
      const filteredLoadTypes = directionLoadTypes.filter((lt) =>
        lt.allowCarrierDockSelection ? lt.docks.length : true
      );

      setLoadTypes(filteredLoadTypes);
      // Auto-fill load type if possible
      switch (true) {
        case filteredLoadTypes.length === 0:
          setError('loadTypeID', {
            message: `This warehouse does not support ${type.toLowerCase()} appointments.`,
          });
          break;

        case selectedWarehouse.warehouseID === SevenElevenMarylandID &&
          type === StopTypes.Dropoff:
          resetField('loadTypeID', {
            defaultValue: '29f0b132-c23f-410d-9cab-1159f678c031',
          });
          break;

        case filteredLoadTypes.length === 1:
          resetField('loadTypeID', {
            defaultValue: filteredLoadTypes[0].id,
          });
          break;

        default:
          break;
      }
    } else {
      toast({
        description:
          loadTypesResponse.error?.message ??
          'Error fetching Load Types for Opendock warehouse.',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    setHasOverriddenSuggestedWarehouse(false);
  }, [type]);

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <div className='mb-4'>
          <form className='w-full flex flex-col mb-2'>
            <Label name=''>Warehouse</Label>
            <DebounceSelect
              showSearch
              placeholder='Choose'
              optionFilterProp='children'
              value={
                selectedWarehouse
                  ? mapWarehousesToOptions([selectedWarehouse])
                  : null
              }
              fetchOptions={handleWarehouseSearch}
              onFocus={handleResetWarehouseSearch}
              onSelect={({ value }) => {
                if (
                  !hasOverriddenSuggestedWarehouse &&
                  value !== selectedWarehouse?.warehouseName
                ) {
                  setHasOverriddenSuggestedWarehouse(true);
                }
                const foundWarehouse = warehouses.find(
                  (w) => w.warehouseID === value
                );
                setSelectedWarehouse(foundWarehouse || null);
              }}
              options={[
                {
                  label: (
                    <Flex direction='col' gap='sm'>
                      <Typography
                        variant='body-sm'
                        className='text-neutral-800 bg-neutral-200 py-0.5 px-1.5 rounded-md w-full'
                      >
                        Type to search warehouses
                      </Typography>
                      <Typography variant='body-xs' weight='medium'>
                        {`${isInitialSearch ? 'Recently used' : 'Searched'} warehouses`}
                      </Typography>
                    </Flex>
                  ),
                  title: `${isInitialSearch ? 'Recently used' : 'Searched'} warehouses`,
                  options: mapWarehousesToOptions(warehouses),
                },
              ]}
              optionRender={(option) => (
                <GenericLocationOption
                  option={option.data}
                  optionFieldsToRender={[
                    'mainAddress',
                    'secondaryAddress',
                    ...(uniqueWarehouseSources ? ['source'] : []),
                  ]}
                />
              )}
              notFoundContent={
                isInitialSearch ? (
                  <Typography variant='body-sm'>
                    Start typing to search for a warehouse
                  </Typography>
                ) : (
                  <Typography variant='body-sm'>No results found</Typography>
                )
              }
            />
          </form>

          {selectedWarehouse && (
            <>
              <WarehouseDisplay warehouse={selectedWarehouse} />

              <div className='mt-2'>
                <form
                  onSubmit={handleSubmit(onSubmitFetchSlots)}
                  className='grid gap-4 grid-cols-1 mt-4 mx-0 w-full'
                >
                  <div className='w-full'>
                    {selectedWarehouse && (
                      <Flex direction='col' gap='lg'>
                        {(warehouseSettings?.referenceNumberIsVisible ||
                          warehouseSettings?.referenceNumberIsRequired) && (
                          <ReferenceNumberInput
                            name='opendockRefNumber'
                            control={control}
                            label={
                              warehouseSettings?.referenceNumberDisplayName ??
                              'Reference Number'
                            }
                            description={
                              warehouseSettings?.referenceNumberHelperText
                            }
                            required={
                              warehouseSettings?.referenceNumberIsRequired
                            }
                            load={load}
                            placeholder='Enter reference number'
                          />
                        )}

                        <div className='w-full'>
                          <OpendockTextInput
                            label='Subscribed Email'
                            name='subscribedEmail'
                            placeholder='<EMAIL>'
                          />
                        </div>

                        <div className='w-full'>
                          <OpendockWithoutLoadTextInput
                            name='notes'
                            label='Notes'
                          />
                        </div>

                        <div className='w-full'>
                          <DateTimeInput
                            control={control}
                            name='startDateTime'
                            label={`Search From (${shortTZLabel})`}
                            preventNormalizedLabelTZ={true}
                            required
                          />
                        </div>

                        <div className='w-full'>
                          <DateTimeInput
                            control={control}
                            name='endDateTime'
                            label={`Search To (${shortTZLabel})`}
                            preventNormalizedLabelTZ={true}
                            required
                          />
                        </div>

                        <div className='w-full'>
                          <Label
                            name='loadTypeID'
                            required={loadTypes?.length > 0}
                          >
                            Load Type
                          </Label>
                          <Controller
                            name='loadTypeID'
                            control={control}
                            disabled={!loadTypes?.length}
                            rules={{
                              required:
                                loadTypes?.length > 0
                                  ? 'This field is required'
                                  : false,
                            }}
                            render={({ field }) => (
                              <Select
                                placeholder='Choose'
                                disabled={!loadTypes?.length}
                                onChange={field.onChange}
                                onSelect={(v: SelectValue) =>
                                  setSelectedLoadType(
                                    loadTypes.find((lt) => lt.id === v)
                                  )
                                }
                                value={field.value}
                                options={loadTypes.map((option) => ({
                                  value: option.id,
                                  label: option.name,
                                }))}
                              />
                            )}
                          />
                          <ErrorMessage
                            errors={errors}
                            name={'loadTypeID'}
                            render={({ message }: { message: string }) => (
                              <Typography
                                variant='body-xs'
                                className='text-error-500'
                              >
                                {message}
                              </Typography>
                            )}
                          />
                        </div>

                        {selectedLoadType?.allowCarrierDockSelection &&
                        selectedLoadType.docks.length ? (
                          <div className='w-full'>
                            <Label
                              name='dockId'
                              required={selectedLoadType.docks.length > 0}
                            >
                              Dock
                            </Label>
                            <Controller
                              name='dockId'
                              control={control}
                              disabled={!selectedLoadType.docks.length}
                              rules={{
                                required:
                                  selectedLoadType.docks.length > 0
                                    ? 'This field is required'
                                    : false,
                              }}
                              render={({ field }) => (
                                <Select
                                  placeholder='Choose Dock'
                                  disabled={!selectedLoadType.docks.length}
                                  onChange={field.onChange}
                                  value={field.value}
                                  options={selectedLoadType.docks.map(
                                    (dock) => ({
                                      value: dock.id,
                                      label: dock.name,
                                    })
                                  )}
                                />
                              )}
                            />
                            <ErrorMessage
                              errors={errors}
                              name={'dockId'}
                              render={({ message }: { message: string }) => (
                                <Typography
                                  variant='body-xs'
                                  className='text-error-500'
                                >
                                  {message}
                                </Typography>
                              )}
                            />
                          </div>
                        ) : null}
                      </Flex>
                    )}

                    {isLoadingCustomFields && <SidebarLoader />}

                    {/* Show custom fields only if they are required for carrier to provide
                        or Drumkit has AI-filled a value */}
                    {!isLoadingCustomFields &&
                      (customApptFieldsTemplate?.findIndex(
                        (value) => value.requiredForCarrier || value.value
                      ) ?? -1) > -1 && (
                        <div className='w-full max-w-full'>
                          <hr className='my-4' />

                          <Typography
                            variant='body-sm'
                            weight='semibold'
                            className='mb-1'
                          >
                            Additional Info Required by Warehouse
                          </Typography>

                          <Flex
                            direction='col'
                            gap='md'
                            className='w-full max-w-full'
                          >
                            {fields.map((field, index) => {
                              if (
                                // Show required fields or ones with AI suggestions
                                (field.hiddenFromCarrier ||
                                  !field.requiredForCarrier) &&
                                !field.value
                              ) {
                                return null;
                              } else {
                                // Render dropdown field
                                if (
                                  field.dropDownValues &&
                                  field.dropDownValues.length > 0
                                ) {
                                  return (
                                    <Flex
                                      key={field.id}
                                      direction='col'
                                      justify='center'
                                      className='w-full min-w-0'
                                    >
                                      <Flex direction='col'>
                                        <Label
                                          name={`customApptFieldsTemplate.${index}.value`}
                                          required={field.requiredForCarrier}
                                          className='break-all overflow-wrap-break-word max-w-full'
                                        >
                                          {titleCase(field.label)}
                                        </Label>
                                        {field.description &&
                                          // Don't duplicate description if it's the same as label
                                          field.description.toLowerCase() !==
                                            field.label.toLowerCase() && (
                                            <span className='text-xs text-neutral-400'>
                                              {toSentenceCase(
                                                field.description
                                              )}
                                            </span>
                                          )}
                                      </Flex>
                                      <Controller
                                        name={
                                          `customApptFieldsTemplate.${index}.value` as FieldPath<OpendockInputsWithoutLoad>
                                        }
                                        rules={{
                                          required:
                                            field.requiredForCarrier &&
                                            'Required',
                                        }}
                                        defaultValue={field.value}
                                        control={control}
                                        render={({ field: dropdownField }) => (
                                          <Select
                                            showSearch
                                            placeholder='Choose'
                                            className='mt-2'
                                            ref={
                                              dropdownField.ref as unknown as React.Ref<any>
                                            }
                                            onBlur={dropdownField.onBlur}
                                            optionFilterProp='children'
                                            filterOption={(
                                              input: string,
                                              option: BaseOptionType | undefined
                                            ) =>
                                              (
                                                option?.label.toLocaleLowerCase() ??
                                                ''
                                              ).includes(
                                                input.toLocaleLowerCase()
                                              )
                                            }
                                            filterSort={(
                                              optionA: BaseOptionType,
                                              optionB: BaseOptionType
                                            ) =>
                                              (optionA?.label ?? '')
                                                .toLowerCase()
                                                .localeCompare(
                                                  (
                                                    optionB?.label ?? ''
                                                  ).toLowerCase()
                                                )
                                            }
                                            onChange={dropdownField.onChange}
                                            value={dropdownField.value}
                                            options={field.dropDownValues?.map(
                                              (option) => ({
                                                value: option,
                                                label: option,
                                              })
                                            )}
                                          />
                                        )}
                                      />
                                      <ErrorMessage
                                        errors={errors}
                                        name={`customApptFieldsTemplate.${index}.value`}
                                        render={({
                                          message,
                                        }: {
                                          message: string;
                                        }) => (
                                          <Typography
                                            variant='body-xs'
                                            className='text-error-500'
                                          >
                                            {message}
                                          </Typography>
                                        )}
                                      />
                                    </Flex>
                                  );
                                } else if (
                                  field.type === 'doc' ||
                                  field.type === 'multidoc'
                                ) {
                                  // Render document upload field
                                  return (
                                    <Flex
                                      key={field.id}
                                      direction='col'
                                      justify='center'
                                      gap='sm'
                                    >
                                      <Typography
                                        variant='body-sm'
                                        weight='medium'
                                      >
                                        {titleCase(field.label)}{' '}
                                        {field.requiredForCarrier && (
                                          <span className='text-error-500'>
                                            *
                                          </span>
                                        )}
                                      </Typography>

                                      {field.description.toLowerCase() !==
                                        field.label.toLowerCase() && (
                                        <Typography
                                          variant='body-xs'
                                          className='text-neutral-600'
                                        >
                                          {field.description}
                                        </Typography>
                                      )}

                                      <AttachmentUpload
                                        maxFileSize={6}
                                        onFilesChange={(
                                          files: ProcessedAttachment[]
                                        ) => {
                                          // Support multiple files (up to 10 for Opendock)
                                          setDocumentFiles((prev) => ({
                                            ...prev,
                                            [field.name]: files,
                                          }));
                                          // Set the field value to indicate files were uploaded
                                          setValue(
                                            `customApptFieldsTemplate.${index}.value`,
                                            files.length > 0
                                              ? 'FILES_UPLOADED'
                                              : ''
                                          );
                                        }}
                                      />
                                    </Flex>
                                  );
                                } else {
                                  // Render text/number field
                                  return (
                                    <Flex
                                      key={field.id}
                                      direction='col'
                                      justify='center'
                                      gap='sm'
                                      className='w-full min-w-0'
                                    >
                                      <RHFTextInput
                                        name={`customApptFieldsTemplate.${index}.value`}
                                        label={titleCase(field.label)}
                                        required={field.requiredForCarrier}
                                        labelClassName='mt-2 w-full min-w-0'
                                        // Don't duplicate description if it's the same as the label
                                        description={
                                          field.description.toLowerCase() !==
                                          field.label.toLowerCase()
                                            ? titleCase(field.description)
                                            : undefined
                                        }
                                        placeholder={field.placeholder}
                                        inputType={
                                          field.type === 'int'
                                            ? 'number'
                                            : undefined
                                        }
                                        step={
                                          field.type === 'int' ? 1 : undefined
                                        }
                                        options={{
                                          // Set value requirements if int
                                          min:
                                            field.type === 'int' &&
                                            field.minLengthOrValue
                                              ? {
                                                  value: field.minLengthOrValue,
                                                  message: `Minimum is ${field.minLengthOrValue}`,
                                                }
                                              : undefined,
                                          max:
                                            field.type === 'int' &&
                                            field.maxLengthOrValue
                                              ? {
                                                  value: field.maxLengthOrValue,
                                                  message: `Maximum is ${field.maxLengthOrValue}`,
                                                }
                                              : undefined,

                                          // Set length requirments if not int
                                          minLength:
                                            field.type !== 'int' &&
                                            field.minLengthOrValue
                                              ? {
                                                  value: field.minLengthOrValue,
                                                  message: `Minimum length is ${field.minLengthOrValue} characters`,
                                                }
                                              : undefined,
                                          maxLength:
                                            field.type !== 'int' &&
                                            field.maxLengthOrValue
                                              ? {
                                                  value: field.maxLengthOrValue,
                                                  message: `Maximum length is ${field.maxLengthOrValue} characters`,
                                                }
                                              : undefined,
                                        }}
                                      />
                                      {(field.label
                                        .toUpperCase()
                                        .includes('PRO OR BOL') ||
                                        field.label
                                          .toUpperCase()
                                          .includes('BOL OR PRO')) &&
                                      load.externalTMSID &&
                                      load.customer.refNumber ? (
                                        <Button
                                          variant={'underline'}
                                          type='button'
                                          className='text-xs p-0 h-6 w-24 ml-auto!'
                                          buttonNamePosthog={null}
                                          onClick={() =>
                                            handleSwapPROAndBOL(
                                              field,
                                              field.value === load.externalTMSID
                                                ? load.customer.refNumber
                                                : load.externalTMSID
                                            )
                                          }
                                        >
                                          <>
                                            {field.value === load.externalTMSID
                                              ? 'Use BOL instead'
                                              : 'Use PRO instead'}
                                          </>
                                        </Button>
                                      ) : null}
                                    </Flex>
                                  );
                                }
                              }
                            })}

                            <Flex justify='center'>
                              <ErrorMessage
                                errors={errors}
                                name={`customApptFieldsTemplate.root`}
                                render={({ message }: { message: string }) => (
                                  <Typography
                                    variant='body-xs'
                                    className='text-error-500'
                                  >
                                    {message}
                                  </Typography>
                                )}
                              />
                            </Flex>
                          </Flex>
                        </div>
                      )}

                    <Button
                      buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                      type='submit'
                      className='w-full mt-4'
                      disabled={loading || !loadTypes?.length}
                      logProperties={{
                        freightTrackingID: getValues('freightTrackingId'),
                        serviceID: load.serviceID,
                      }}
                    >
                      {loading ? <ButtonLoader /> : ButtonText.GetOpenApptSlots}
                    </Button>
                  </div>
                </form>
              </div>

              <section ref={slotsContainerRef}>
                {loading && null}

                {orderedSlots !== undefined &&
                  orderedSlots !== null &&
                  Object.keys(orderedSlots.slots).length > 0 && (
                    <>
                      <div className='mt-8 mb-4 mr-8'>
                        <Flex direction='row' gap='xs'>
                          <Typography weight='medium'>
                            Warehouse Timezone:
                          </Typography>
                          <Typography weight='bold'>
                            {` ${shortTZLabel}`}
                          </Typography>
                        </Flex>
                      </div>
                      {Object.keys(orderedSlots.slots).map((date) => (
                        <div key={date}>
                          <Typography
                            variant='h6'
                            weight='bold'
                            className='text-neutral-600 uppercase mt-4'
                          >
                            {date}
                          </Typography>
                          <Grid cols='3' gap='xs' className='mt-2 mx-0 w-full'>
                            {orderedSlots.slots[date].map(
                              (slot: GroupedSlot, idx: number) => (
                                <TimeSlotButton
                                  key={idx}
                                  slot={slot}
                                  isSelected={selectedSlot === slot}
                                  onSelect={(clickedSlot) =>
                                    setSelectedSlot(
                                      selectedSlot === clickedSlot
                                        ? null
                                        : clickedSlot
                                    )
                                  }
                                />
                              )
                            )}
                          </Grid>
                        </div>
                      ))}
                      {selectedSlot ? (
                        <div className='mt-4 text-neutral-400 text-left text-sm'>
                          <Typography weight='bold' className='my-1'>
                            Selected Slot:
                          </Typography>
                          <Typography className='mb-2'>
                            {selectedSlot.startTime.toLocaleString('en-US', {
                              timeZone: selectedSlot.timezone,
                              timeZoneName: 'short',
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: 'numeric',
                              minute: 'numeric',
                              second: undefined,
                              hour12: false,
                            })}
                          </Typography>
                          {apptConfirmationNumber ? (
                            <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                              <Typography className='mb-2'>
                                Appointment confirmed 🎉
                              </Typography>
                              <Typography variant='body-sm' className='mb-4'>
                                <Typography variant='body-sm' weight='bold'>
                                  Opendock Confirmation #:{' '}
                                </Typography>
                                {apptConfirmationNumber}
                              </Typography>

                              <Typography variant='body-sm' className='mb-1'>
                                {isTMSUpdateLoadAppointmentSupported(tmsName) &&
                                isAppointmentTMSUpdateEnabled
                                  ? tmsUpdateSucceeded
                                    ? 'Your TMS was updated with the appointment details'
                                    : 'Your TMS update will run in the background.'
                                  : 'Make sure to update your TMS with the scheduled appointment.'}
                              </Typography>
                            </div>
                          ) : null}
                          <Button
                            buttonNamePosthog={
                              ButtonNamePosthog.ConfirmSlotApptScheduling
                            }
                            className='mt-2 w-full'
                            onClick={handleConfirmAppointment}
                            disabled={loading || !loadTypes?.length}
                          >
                            {loading ? (
                              <ButtonLoader />
                            ) : (
                              ButtonText.ConfirmSlotApptScheduling
                            )}
                          </Button>
                        </div>
                      ) : null}

                      {/* TMS Update Form - Visible after successful appointment confirmation and if TMS update is supported */}
                      {selectedSlot &&
                      apptConfirmationNumber &&
                      showUpdateTMSLoadWithAppt ? (
                        <UpdateTMSLoadWithAppt
                          load={load}
                          stopType={type}
                          appointmentStartTime={selectedSlot.startTime}
                          appointmentEndTime={
                            new Date(
                              selectedSlot.startTime.getTime() + 60 * 60 * 1000
                            )
                          }
                          timezone={selectedSlot.timezone}
                          freightTrackingId={getValues('freightTrackingId')}
                          onSuccess={() => setTMSUpdateSucceeded(true)}
                          onError={(error) =>
                            console.error('TMS update failed:', error)
                          }
                        />
                      ) : null}
                    </>
                  )}
              </section>
            </>
          )}
        </div>
      </FormProvider>
    </ExtendedFormProvider>
  );
}

interface OpendockInputsWithoutLoad {
  appt: {
    addressLine1: MaybeUndef<string>;
    expectedPickupTime: MaybeUndef<string>;
    expectedDeliveryTime: MaybeUndef<string>;
  };
  externalTMSID: string; // TMS's UUID for the shipment
  freightTrackingId: string; // PRO Number
  // Ref Number requirements is in warehouse settings, not custom appt fields
  // and can be either 1) FreightTrackingID/PRO, 2) PO #, 3) Load.Customer/Pickup/Consignee.RefNumber,
  // based on the warehouse's label/description
  opendockRefNumber: string;
  loadTypeID: string;
  dockId: string;
  startDateTime: Date;
  endDateTime: Date;
  warehouse: any;
  subscribedEmail: string; // TODO: support multiple emails
  customApptFieldsTemplate: CustomApptFieldsTemplate[];
  notes: string;
}

type OpendockWithoutLoadTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<OpendockInputsWithoutLoad> };

const OpendockWithoutLoadTextInput = (
  props: OpendockWithoutLoadTextInputProps
) => <RHFTextInput {...props} />;

type OpendockTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<OpendockInputsWithoutLoad>;
};
const OpendockTextInput = (props: OpendockTextInputProps) => (
  <RHFTextInput {...props} />
);

export function isSevenElevenWH(whID: string): boolean {
  return whID === SevenElevenMarylandID || whID === SevenElevenVirginiaID;
}
