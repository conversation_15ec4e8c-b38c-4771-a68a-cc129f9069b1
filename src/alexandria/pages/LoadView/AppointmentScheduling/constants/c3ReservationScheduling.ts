export type TenantShowFields = {
  contactName: boolean;
  email: boolean;
  phone: boolean;
  unloadType: boolean;
  casesOrPalletQuantity: boolean;
  loadType: boolean;
  carrierCC: boolean;
  comment: boolean;
};

export type TenantRequiredFields = {
  contactName: boolean;
  email: boolean;
  unloadType: boolean;
  casesOrPalletQuantity: boolean;
  loadType: boolean;
};

export type TenantFieldConfig = {
  showFields: TenantShowFields;
  requiredFields: TenantRequiredFields;
};

export type TenantReservationTypeOption = {
  value: string;
  label: string;
  supported: boolean;
  fieldConfig: TenantFieldConfig;
};

export type TenantConfig = {
  slug: string;
  displayName: string;
  supported: boolean;
  requiresReservationType?: boolean;
  fieldConfig: TenantFieldConfig;
  reservationTypes?: TenantReservationTypeOption[];
};

const BASE_SHOW_FIELDS: TenantShowFields = {
  contactName: false,
  email: false,
  phone: false,
  unloadType: false,
  casesOrPalletQuantity: false,
  loadType: false,
  carrierCC: false,
  comment: false,
};

const BASE_REQUIRED_FIELDS: TenantRequiredFields = {
  contactName: false,
  email: false,
  unloadType: false,
  casesOrPalletQuantity: false,
  loadType: false,
};

const createFieldConfig = (
  showOverrides: Partial<TenantShowFields> = {},
  requiredOverrides: Partial<TenantRequiredFields> = {}
): TenantFieldConfig => ({
  showFields: {
    ...BASE_SHOW_FIELDS,
    ...showOverrides,
  },
  requiredFields: {
    ...BASE_REQUIRED_FIELDS,
    ...requiredOverrides,
  },
});

const DEFAULT_FIELD_CONFIG = createFieldConfig({
  contactName: true,
  email: true,
  phone: true,
  comment: true,
});

const DOLLAR_TREE_FIELD_CONFIG = createFieldConfig(
  {
    contactName: true,
    email: true,
    phone: true,
    unloadType: true,
    casesOrPalletQuantity: true,
    comment: true,
  },
  {
    contactName: true,
    email: true,
    unloadType: true,
    casesOrPalletQuantity: true,
  }
);

const KEHE_FIELD_CONFIG = createFieldConfig(
  {
    loadType: true,
    carrierCC: true,
    comment: true,
  },
  {
    loadType: true,
  }
);

const GENERIC_CONTACT_FIELD_CONFIG = createFieldConfig({
  contactName: true,
  email: true,
  phone: true,
  comment: true,
});

const LECLERC_RESERVATION_TYPES: TenantReservationTypeOption[] = [
  {
    value: 'Bulk Tanker',
    label: 'Bulk Tanker',
    supported: true,
    fieldConfig: createFieldConfig(
      {
        contactName: true,
        email: true,
        phone: true,
        comment: true,
      },
      { contactName: true, email: true }
    ),
  },
  {
    value: 'Inbound',
    label: 'Inbound',
    supported: true,
    fieldConfig: createFieldConfig(
      {
        contactName: true,
        email: true,
        phone: true,
        comment: true,
      },
      { contactName: true }
    ),
  },
  {
    value: 'Inbound 1 or 2',
    label: 'Inbound 1 or 2',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
  {
    value: 'Outbound',
    label: 'Outbound',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
  {
    value: 'Outbound Drop',
    label: 'Outbound Drop',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
  {
    value: 'Outbound LTL',
    label: 'Outbound LTL',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
];

const TENANT_CONFIGS: Record<string, TenantConfig> = {
  dollartree: {
    slug: 'dollartree',
    displayName: 'Dollar Tree',
    supported: true,
    fieldConfig: DOLLAR_TREE_FIELD_CONFIG,
  },
  kehe: {
    slug: 'kehe',
    displayName: 'KeHE',
    supported: false,
    fieldConfig: KEHE_FIELD_CONFIG,
  },
  savealot: {
    slug: 'savealot',
    displayName: 'Save A Lot',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
  cswg: {
    slug: 'cswg',
    displayName: 'CSWG',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
  schnucks: {
    slug: 'schnucks',
    displayName: 'Schnucks',
    supported: false,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
  },
  leclerc: {
    slug: 'leclerc',
    displayName: 'Leclerc',
    supported: false,
    requiresReservationType: true,
    fieldConfig: GENERIC_CONTACT_FIELD_CONFIG,
    reservationTypes: LECLERC_RESERVATION_TYPES,
  },
};

const DEFAULT_TENANT_CONFIG: TenantConfig = {
  slug: 'default',
  displayName: 'Tenant',
  supported: false,
  fieldConfig: DEFAULT_FIELD_CONFIG,
};

export const getTenantConfig = (tenantSlug: string): TenantConfig => {
  if (!tenantSlug) {
    return DEFAULT_TENANT_CONFIG;
  }
  return TENANT_CONFIGS[tenantSlug.toLowerCase()] || DEFAULT_TENANT_CONFIG;
};

export const getTenantFieldConfig = (
  tenantSlug: string,
  reservationType?: string
): TenantFieldConfig => {
  const tenantConfig = getTenantConfig(tenantSlug);
  if (reservationType && tenantConfig.reservationTypes) {
    const match = tenantConfig.reservationTypes.find(
      (option) => option.value.toLowerCase() === reservationType.toLowerCase()
    );
    if (match) {
      return match.fieldConfig;
    }
  }

  return tenantConfig.fieldConfig;
};

export const getReservationTypeOption = (
  tenantConfig: TenantConfig,
  reservationType?: string
): TenantReservationTypeOption | undefined => {
  if (!reservationType || !tenantConfig.reservationTypes) {
    return undefined;
  }

  return tenantConfig.reservationTypes.find(
    (option) => option.value.toLowerCase() === reservationType.toLowerCase()
  );
};

const SUPPORTED_TENANT_NAMES = Object.values(TENANT_CONFIGS)
  .filter((config) => config.supported)
  .map((config) => config.displayName)
  .sort();

const UNSUPPORTED_TENANT_NAMES = Object.values(TENANT_CONFIGS)
  .filter((config) => !config.supported)
  .map((config) => config.displayName)
  .sort();

export const SUPPORTED_TENANT_LABEL =
  SUPPORTED_TENANT_NAMES.length > 0
    ? SUPPORTED_TENANT_NAMES.join(', ')
    : 'None';

export const UNSUPPORTED_TENANT_LABEL =
  UNSUPPORTED_TENANT_NAMES.length > 0
    ? UNSUPPORTED_TENANT_NAMES.join(', ')
    : 'None';
