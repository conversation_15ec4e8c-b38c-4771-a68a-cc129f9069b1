import { useEffect, useState } from 'react';
import { MoreHorizontal } from 'lucide-react';

import { Button } from 'components/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from 'components/Dropdown';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { useToast } from 'hooks/useToaster';
import { getAppointments, type Appointment } from 'lib/api/getAppointments';
import { NormalizedLoad } from 'types/Load';
import { isCancelSupported, isRescheduleSupported } from 'constants/AppointmentSupport';
import captureException from 'utils/captureException';

type AppointmentsListProps = {
  load: NormalizedLoad;
};

export function AppointmentsList({ load }: AppointmentsListProps) {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchAppointments = async () => {
      if (!load.freightTrackingID) {
        return;
      }

      setIsLoading(true);
      try {
        const result = await getAppointments(load.freightTrackingID);
        
        if (result.isOk()) {
          setAppointments(result.value.appointments || []);
        } else {
          console.error('Failed to fetch appointments:', result.error);
          toast({
            description: 'Failed to fetch appointments',
            variant: 'destructive',
          });
        }
      } catch (error) {
        captureException(error, { functionName: 'fetchAppointments' });
        toast({
          description: 'Error fetching appointments',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointments();
  }, [load.freightTrackingID, toast]);

  // Don't render anything if no appointments
  if (!isLoading && appointments.length === 0) {
    return null;
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    try {
      return new Date(timeString).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return timeString;
    }
  };

  if (isLoading) {
    return (
      <div className="my-4">
        <Typography variant="h5" weight="medium" className="mb-2">
          Scheduled Appointments
        </Typography>
        <div className="text-sm text-neutral-600">Loading appointments...</div>
      </div>
    );
  }

  return (
    <div className="my-4">
      <Typography variant="h5" weight="medium" className="mb-2">
        Scheduled Appointments
      </Typography>
      <div className="space-y-2">
        {appointments.map((appointment) => {
          const canCancel = isCancelSupported(appointment.Source, appointment.tenant);
          const canReschedule = isRescheduleSupported(appointment.Source, appointment.tenant);
          
          return (
            <div
              key={appointment.ID}
              className="border border-neutral-200 rounded-md p-3 bg-white"
            >
              <Flex justify="between" align="start">
                <div className="flex-1">
                  <Flex align="center" gap="sm" className="mb-1">
                    <Typography variant="body-sm" weight="medium">
                      {appointment.customerName}
                    </Typography>
                    <span className="text-xs px-2 py-1 bg-neutral-100 rounded text-neutral-600">
                      {appointment.Status.replace(/"/g, '')}
                    </span>
                  </Flex>
                  
                  <div className="text-sm text-neutral-600 space-y-1">
                    <div>
                      <strong>Date:</strong> {formatDate(appointment.Date)}
                    </div>
                    {appointment.StartTime && (
                      <div>
                        <strong>Time:</strong> {formatTime(appointment.StartTime)}
                      </div>
                    )}
                    {appointment.ExternalID && (
                      <div>
                        <strong>Confirmation:</strong> {appointment.ExternalID}
                      </div>
                    )}
                    {appointment.Notes && (
                      <div>
                        <strong>Notes:</strong> {appointment.Notes}
                      </div>
                    )}
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem disabled={!canReschedule}>
                      Reschedule
                    </DropdownMenuItem>
                    <DropdownMenuItem disabled={!canCancel}>
                      Cancel
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </Flex>
            </div>
          );
        })}
      </div>
    </div>
  );
}
