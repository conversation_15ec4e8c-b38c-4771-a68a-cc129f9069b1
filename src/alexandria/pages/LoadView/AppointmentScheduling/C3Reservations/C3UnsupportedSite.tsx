import { AlertCircleIcon } from 'lucide-react';

import { Flex } from 'components/layout';
import { Typography } from 'components/typography';

interface C3UnsupportedSiteProps {
  formattedTenantUrl: string;
  hasTenantUrlLink: boolean;
  onOpenTenantUrl: () => void;
}

export function C3UnsupportedSite({
  formattedTenantUrl,
  hasTenantUrlLink,
  onOpenTenantUrl,
}: C3UnsupportedSiteProps) {
  return (
    <Flex
      direction='col'
      align='start'
      gap='xs'
      className='rounded-lg border border-error-200 bg-error-50 p-3 w-full'
    >
      <Flex align='center' gap='sm'>
        <AlertCircleIcon className='h-4 w-4 text-error-600' />
        <Typography variant='body-sm' className='text-error-600'>
          Website Not Supported
        </Typography>
      </Flex>

      {hasTenantUrlLink && (
        <Flex align='center' gap='xs' wrap='wrap'>
          <Typography variant='body-xs' className='text-error-600 uppercase'>
            url:
          </Typography>
          <button
            type='button'
            onClick={onOpenTenantUrl}
            className='text-left text-error-600 underline focus:outline-none cursor-pointer'
          >
            <Typography variant='body-xs' className='text-error-600 break-all'>
              {formattedTenantUrl}
            </Typography>
          </button>
        </Flex>
      )}
    </Flex>
  );
}
