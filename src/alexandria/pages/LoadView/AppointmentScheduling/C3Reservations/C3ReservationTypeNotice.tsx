import { AlertCircleIcon } from 'lucide-react';

import { Flex } from 'components/layout';
import { Typography } from 'components/typography';

interface C3ReservationTypeNoticeProps {
  message: string;
}

export function C3ReservationTypeNotice({
  message,
}: C3ReservationTypeNoticeProps) {
  if (!message) {
    return null;
  }

  return (
    <Flex
      align='center'
      gap='sm'
      className='rounded-lg border border-error-200 bg-error-50 p-3 w-full'
    >
      <AlertCircleIcon className='h-4 w-4 text-error-600' />
      <Typography variant='body-sm' className='text-error-600'>
        {message}
      </Typography>
    </Flex>
  );
}
