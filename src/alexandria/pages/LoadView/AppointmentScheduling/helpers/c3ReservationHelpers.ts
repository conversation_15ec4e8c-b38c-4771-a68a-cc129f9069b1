export const extractTenantSlug = (tenant?: string): string => {
  if (!tenant) return '';

  const trimmed = tenant.trim();
  if (!trimmed) return '';

  const lower = trimmed.toLowerCase();
  const match = lower.match(/c3reservations\.com\/([^/]+)/);
  if (match && match[1]) {
    return match[1];
  }

  try {
    const url = new URL(
      lower.startsWith('http') ? lower : `https://${lower.replace(/^\/+/, '')}`
    );
    const pathSegments = url.pathname
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pathSegments.length > 0) {
      return pathSegments[0];
    }
  } catch {
    const pieces = lower
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pieces.length > 0) {
      return pieces[0];
    }
  }

  return '';
};

export const extractTenantName = (tenant?: string): string => {
  if (!tenant) return '';

  const trimmed = tenant.trim();
  if (!trimmed) return '';

  const match = trimmed.match(/c3reservations\.com\/([^/]+)/i);
  if (match && match[1]) {
    return match[1];
  }

  try {
    const url = new URL(
      trimmed.startsWith('http')
        ? trimmed
        : `https://${trimmed.replace(/^\/+/, '')}`
    );
    const pathSegments = url.pathname
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pathSegments.length > 0) {
      return pathSegments[0];
    }
  } catch {
    const pieces = trimmed
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pieces.length > 0) {
      return pieces[0];
    }
  }

  return '';
};

export const formatTenantUrl = (
  tenant?: string
): { display: string; href: string } => {
  if (!tenant) {
    return { display: '', href: '' };
  }

  const raw = tenant.trim();
  if (!raw) {
    return { display: '', href: '' };
  }

  let href = raw;
  if (!/^https?:\/\//i.test(href)) {
    href = `https://${href.replace(/^\/+/, '')}`;
  }

  try {
    const parsed = new URL(href);
    href = parsed.toString();
  } catch {
    try {
      const parsed = new URL(`https://${raw.replace(/^\/+/, '')}`);
      href = parsed.toString();
    } catch {
      return { display: raw, href: '' };
    }
  }

  let display = href.replace(/^https?:\/\//i, '');
  display = display.replace(/\/app\/login\/?$/i, '');
  display = display.replace(/\/$/, '');

  return { display, href };
};
