import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  Form<PERSON>rovider,
  SubmitError<PERSON><PERSON><PERSON>,
  SubmitHandler,
  useForm,
} from 'react-hook-form';

import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {
  BoxIcon,
  Building2,
  CircleDollarSignIcon,
  CircleUserRound,
  ExternalLinkIcon,
  Info,
  MapPinnedIcon,
  NotebookIcon,
  ReceiptIcon,
  TruckIcon,
  WarehouseIcon,
  Weight,
} from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

import { Accordion } from 'components/Accordion';
import { Button } from 'components/Button';
import { ScrollUntilComponent } from 'components/ScrollUntilComponent';
import { FormatPhoneNumber } from 'components/input/RHFTextInput';
import ButtonLoader from 'components/loading/ButtonLoader';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { isUnsupportedSection } from 'contexts/load';
import {
  SidebarState,
  SidebarStateContext,
} from 'contexts/sidebarStateContext';
import { useFieldAttributes, useLoadContext } from 'hooks/useLoadContext';
import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { useServiceFeatures } from 'hooks/useServiceContext';
import useTMSContext from 'hooks/useTMSContext';
import { useToast } from 'hooks/useToaster';
import { applyLoadSuggestion } from 'lib/api/applyLoadSuggestion';
import { getCarriers } from 'lib/api/getCarriers';
import { getCustomers } from 'lib/api/getCustomers';
import { getLocations } from 'lib/api/getLocations';
import { getOperators } from 'lib/api/getOperators';
import { updateTMS } from 'lib/api/updateTMS';
import { OperatorSectionForm } from 'pages/LoadView/LoadInformation/Sections/OperatorSectionForm';
import UnifiedAljexLoadForm from 'pages/QuoteView/LoadBuilding/AljexSectionForms/UnifiedAljexLoadForm';
import { RatesForm } from 'pages/QuoteView/LoadBuilding/McleodSectionForms/Rates';
import QuantumEdgeLoadForm from 'pages/QuoteView/LoadBuilding/QuantumEdgeLoadForm';
import { Email } from 'types/Email';
import { ExternalLinks } from 'types/ExternalLinks';
import {
  Load,
  NormalizedLoad,
  TMSCarrier,
  TMSCustomer,
  TMSLocation,
  normalizeLoad,
} from 'types/Load';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { TMSOperation } from 'types/api/UpdateTMS';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import Pageview from 'types/enums/Pageview';
import {
  GenericSuggestion,
  SuggestionPipelines,
} from 'types/suggestions/CoreSuggestions';
import {
  ApptConfirmationChanges,
  CarrierChanges,
  LoadChanges,
  SuggestedLoadChange,
  SuggestedLoadChange as suggestion,
} from 'types/suggestions/LoadSuggestions';
import { isElementInView } from 'utils/isElementInView';
import {
  injectSelectedObject,
  resolveTransportType,
} from 'utils/loadInfoAndBuilding';
import {
  denormalizeDatesForTMSForm,
  normalizeDateForTMS,
} from 'utils/parseDatesForTMSForm';
import { handleClickedSuggestionAndGetElements } from 'utils/suggestionScroll';

import { LoadSectionAccordionItem } from './LoadInformation/Components/LoadSectionAccordionItem';
import { BillToSectionForm } from './LoadInformation/Sections/BillToSectionForm';
import { CarrierSectionForm } from './LoadInformation/Sections/CarrierSectionForm';
import { ConsigneeSectionForm } from './LoadInformation/Sections/ConsigneeSectionForm';
import { CustomerSectionForm } from './LoadInformation/Sections/CustomerSectionForm';
import { NotesSectionForm } from './LoadInformation/Sections/GlobalTranzTMS/NotesSectionForm';
import { PickupSectionForm } from './LoadInformation/Sections/PickupSectionForm';
import { RouteSectionForm } from './LoadInformation/Sections/RouteSectionForm';
import { SpecificationsForm } from './LoadInformation/Sections/SpecificationsForm';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

type LoadInformationTabProps = {
  load: Load;
  suggestion?: suggestion;
  showSuggestion?: boolean;
  externalLinks: MaybeUndef<ExternalLinks>;
};

export default function LoadInformationTab({
  load,
  externalLinks,
}: LoadInformationTabProps) {
  useLogPostHogPageView(Pageview.LoadInformation, {
    service_id: load.serviceID,
    load_id: load.ID,
    freightTrackingID: load.freightTrackingID,
  });
  const { tmsName, tmsID, tenant } = useTMSContext();

  const { toast } = useToast();
  const { fieldAttributes, invalidateLoad } = useLoadContext();
  const allFieldAttrs = useFieldAttributes();

  // For non-NFI Aljex clients, we use the UnifiedAljexLoadForm component
  // TODO: ENG-4265
  if (tmsName === TMS.Aljex && !tenant.toLowerCase().includes('nfi')) {
    return (
      <UnifiedAljexLoadForm
        isCreateMode={false}
        load={load}
        onSuccess={async (result) => {
          if (result.load && result.loadAttributes) {
            await invalidateLoad(result.load, result.loadAttributes);
          } else {
            await invalidateLoad();
          }
        }}
        onError={async (error) => {
          if (error.load && error.loadAttributes) {
            await invalidateLoad(error.load, error.loadAttributes);
          } else {
            await invalidateLoad();
          }
        }}
      />
    );
  }

  const [suggestedFieldsEdited, setSuggestedFieldsEdited] = useState<
    Record<string, boolean>
  >({});
  const [operators, setOperators] = useState<Array<string>>([]);
  const [loading, setLoading] = useState(false);
  const [activeTabs, setActiveTabs] = useState<string[]>([]);

  const [suggestionScrollTo, setSuggestionScrollTo] =
    useState<Maybe<HTMLElement>>();
  const [suggestionMustScrollUntil, setSuggestionMustScrollUntil] =
    useState<Maybe<HTMLElement>>();
  const [showScrollUntilComponent, setShowScrollUntilComponent] =
    useState<boolean>(false);

  const [suggestedFieldsPrevValues, setSuggestedFieldsPrevValues] =
    useState<LoadChanges>();

  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [locations, setLocations] = useState<Maybe<TMSLocation[]>>(null);

  const [isLoadingCustomers, setIsLoadingCustomers] = useState(true);
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);

  const [isLoadingCarriers, setIsLoadingCarriers] = useState(true);
  const [carriers, setCarriers] = useState<Maybe<TMSCarrier[]>>(null);

  const [carrierIsQualified, setCarrierIsQualified] = useState(true);

  const posthog = usePostHog();

  const {
    serviceFeaturesEnabled: {
      isTurvoSectionLinksEnabled,
      isOperatorEnabled,
      isMultiStopLoadViewEnabled,
    },
    tmsIntegrations,
  } = useServiceFeatures();

  const isTaiTMS = tmsIntegrations?.some((tms) => tms.name === TMS.Tai);

  const {
    currentState: { drumkitAppContainer, clickedSuggestion },
    setCurrentState,
  } = useContext(SidebarStateContext);

  // Collapsing sections and updating fields after Email AI card is clicked
  useEffect(() => {
    if (
      !clickedSuggestion ||
      (clickedSuggestion as SuggestedLoadChange).freightTrackingID !=
        load.freightTrackingID
    )
      return;

    switch (clickedSuggestion.pipeline) {
      case SuggestionPipelines.CarrierInfo:
        if (!activeTabs.includes('carrier')) {
          setActiveTabs((oldTabs) => [...oldTabs, 'carrier']);
        }
        break;
      case SuggestionPipelines.ApptConfirmation:
        if (
          clickedSuggestion.suggested.pickupApptTime &&
          !activeTabs.includes('pickup')
        ) {
          setActiveTabs((oldTabs) => [...oldTabs, 'pickup']);
        }
        if (
          clickedSuggestion.suggested.dropoffApptTime &&
          !activeTabs.includes('consignee')
        ) {
          setActiveTabs((oldTabs) => [...oldTabs, 'consignee']);
        }
        break;
    }

    handleClickedSuggestionAndGetElements(clickedSuggestion).then((value) => {
      value.scrollTo && setSuggestionScrollTo(value.scrollTo);
      value.mustScrollUntil &&
        setSuggestionMustScrollUntil(value.mustScrollUntil);
    });

    setSuggestedFieldsPrevValues(
      Object.keys(clickedSuggestion.suggested).reduce(
        (res, field) => ({
          ...res,
          [field]: memoizedDefaultValues.carrier[field as keyof CarrierChanges],
        }),
        {} as LoadChanges
      )
    );

    const autoFilledFields = Object.keys(clickedSuggestion.suggested);
    setSuggestedFieldsEdited(
      autoFilledFields.reduce(
        (acc, field) => ({
          ...acc,
          [field]: false,
        }),
        {}
      )
    );
  }, [clickedSuggestion]);

  const handleFieldChange = (
    fieldName: string,
    oldValue: any,
    newValue: any
  ) => {
    if (
      clickedSuggestion &&
      Object.hasOwnProperty.call(suggestedFieldsEdited, fieldName) &&
      !suggestedFieldsEdited[fieldName]
    ) {
      setSuggestedFieldsEdited((prev) => ({
        ...prev,
        [fieldName]: true,
      }));

      posthog.capture('load_information_suggestion_field_edited', {
        field_name: fieldName,
        original_value: oldValue,
        new_value: newValue,
        suggestion_id: clickedSuggestion.id,
        suggestion_type: clickedSuggestion.pipeline,
        service_id: load.serviceID,
        load_id: load.ID,
      });
    }
  };

  useEffect(() => {
    if (suggestionScrollTo && suggestionMustScrollUntil) {
      suggestionScrollTo.scrollIntoView({ behavior: 'smooth' });

      setTimeout(() => {
        if (isElementInView(drumkitAppContainer, suggestionMustScrollUntil)) {
          drumkitAppContainer?.removeEventListener('scroll', onScroll);
        } else {
          setShowScrollUntilComponent(true);
        }
      }, 1000);
    }
  }, [suggestionScrollTo]);

  // Fetch TMS objects
  const fetchOperators = async () => {
    if (isOperatorEnabled && tmsName !== TMS.ThreeG) {
      const res = await getOperators(tmsID);
      if (res.isOk()) {
        setOperators(res.value);
      }
    }
  };

  useEffect(() => {
    if (isOperatorEnabled) {
      fetchOperators();
    }
  }, []);

  const fetchCustomers = async () => {
    if (tmsName !== TMS.McleodEnterprise) {
      return;
    }

    setIsLoadingCustomers(true);

    const res = await getCustomers(tmsID);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
    } else {
      toast({
        description: 'Error while fetching customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  const fetchCarriers = async () => {
    if (tmsName !== TMS.McleodEnterprise) {
      return;
    }

    setIsLoadingCarriers(true);

    const res = await getCarriers(tmsID);
    if (res.isOk()) {
      setCarriers(res.value.carrierList);
    } else {
      toast({
        description: 'Error while fetching carrier list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCarriers(false);
  };

  // Optimization: Defined here instead of in the component to avoid multiple API lookups
  const fetchLocations = async () => {
    // NOTE: Only Mcleod GET objects supported on Load Info rn
    if (tmsName !== TMS.McleodEnterprise) {
      return;
    }
    setIsLoadingLocations(true);

    const res = await getLocations(tmsID);
    if (res.isOk()) {
      setLocations(res.value.locationList);
    } else {
      toast({
        description: 'Error while fetching location list.',
        variant: 'destructive',
      });
    }
    setIsLoadingLocations(false);
  };

  const handleRefreshLocations = async () => {
    setIsLoadingLocations(true);

    const res = await getLocations(tmsID, true);
    if (res.isOk()) {
      setLocations(res.value.locationList);
      toast({
        description: 'Successfully refreshed location list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing location list.',
        variant: 'destructive',
      });
    }
    setIsLoadingLocations(false);
  };

  useEffect(() => {
    fetchCustomers();
    fetchLocations();
    fetchCarriers();
  }, []);

  // Memoize scroll handler so that we can reliably add / remove it
  const onScroll = useCallback(() => {
    if (!clickedSuggestion) return;

    const isInView = isElementInView(
      drumkitAppContainer,
      suggestionMustScrollUntil
    );

    // Once the element is in view we can hide the helper and detach the listener
    if (isInView) {
      setShowScrollUntilComponent(false);
      // Listener will be removed in the cleanup returned by useEffect
    }
  }, [clickedSuggestion, drumkitAppContainer, suggestionMustScrollUntil]);

  // Attach scroll listener only when necessary and always clean it up to prevent leaks
  useEffect(() => {
    if (!drumkitAppContainer) return;

    drumkitAppContainer.addEventListener('scroll', onScroll, {
      passive: true,
    });

    // Cleanup on dependency change or component unmount
    return () => {
      drumkitAppContainer.removeEventListener('scroll', onScroll);
    };
  }, [drumkitAppContainer, onScroll]);

  const memoizedDefaultValues: NormalizedLoad = useMemo(() => {
    const normalizedLoad = normalizeLoad(tmsName, load);
    const suggestedFields = clickedSuggestion?.suggested as LoadChanges;

    // Pre-map transportType for non-McLeod TMS
    if (tmsName !== TMS.McleodEnterprise && normalizedLoad.specifications) {
      const originalTransportType = normalizedLoad.specifications.transportType;

      (normalizedLoad.specifications.transportType as string | null) =
        resolveTransportType(originalTransportType);
    }

    return getLoadInfoWithSuggestion(normalizedLoad, suggestedFields);
  }, [tmsName, load, clickedSuggestion]);

  const formMethods = useForm<NormalizedLoad>({
    defaultValues: memoizedDefaultValues,
  });

  // Update form values when suggestion changes
  useEffect(() => {
    if (memoizedDefaultValues) {
      formMethods.reset(memoizedDefaultValues);
    }
  }, [memoizedDefaultValues]);

  useEffect(() => {
    const addtlLocs: TMSLocation[] = [];
    if (memoizedDefaultValues.pickup.externalTMSID) {
      addtlLocs.push(memoizedDefaultValues.pickup);
    }

    if (memoizedDefaultValues.consignee.externalTMSID) {
      addtlLocs.push(memoizedDefaultValues.consignee);
    }

    setLocations((prevLocations) => {
      let updatedLocations = prevLocations ?? [];

      addtlLocs.forEach((loc) => {
        updatedLocations = injectSelectedObject(loc, updatedLocations);
      });

      return updatedLocations;
    });
  }, [
    // TODO: We should be able to store the initial selected value
    // without constantly injecting into the list like in Load Building
    // page. It's causing glitchy loading right now.
    locations,
    memoizedDefaultValues?.pickup.externalTMSID,
    memoizedDefaultValues?.consignee.externalTMSID,
  ]);

  useEffect(() => {
    const customerID = memoizedDefaultValues.customer?.externalTMSID;
    if (customerID) {
      setCustomers((prevCustomers) =>
        injectSelectedObject(
          memoizedDefaultValues.customer,
          prevCustomers ?? []
        )
      );
    }
  }, [customers, memoizedDefaultValues?.customer?.externalTMSID]);

  useEffect(() => {
    const carrierID = memoizedDefaultValues.carrier?.externalTMSID;
    const selectedObj = carriers?.find((c) => c.externalTMSID === carrierID);

    if (carrierID && carriers) {
      setCarriers((prevCarriers) =>
        injectSelectedObject(
          selectedObj ??
            ({
              name: memoizedDefaultValues.carrier?.name,
              externalTMSID: memoizedDefaultValues.carrier?.externalTMSID,
            } as TMSCarrier),
          prevCarriers ?? []
        )
      );
    }
  }, [carriers, memoizedDefaultValues?.carrier?.externalTMSID]);
  const { handleSubmit } = formMethods;

  const onSubmit: SubmitHandler<NormalizedLoad> = async (data) => {
    if (isTaiTMS) {
      toast({
        description:
          "Tai currently doesn't allow shipment updates in the sidebar.",
        variant: 'info',
      });
      return;
    }

    if (clickedSuggestion) {
      const editedFields = Object.entries(suggestedFieldsEdited)
        .filter(([_, wasEdited]) => wasEdited)
        .map(([field]) => field);

      posthog.capture('load_information_suggestion_summary', {
        suggestion_id: clickedSuggestion.id,
        suggestion_type: clickedSuggestion.pipeline,
        total_suggested_fields: Object.keys(suggestedFieldsEdited).length,
        edited_fields_count: editedFields.length,
        edited_fields: editedFields,
        service_id: load.serviceID,
        load_id: load.ID,
      });
    }

    if (!carrierIsQualified) {
      // Prevent form submission if there are validation errors
      toast({
        description: data.carrier.name + ' is not qualified for this load.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    const reqData = buildUpdateLoadRequest(load, data, tmsName);

    const res = await updateTMS(load.ID!, reqData);

    if (res.isOk()) {
      toast({
        description: res.value.message,
        variant: 'success',
      });

      if (res.value.load && res.value.loadAttributes) {
        await invalidateLoad(res.value.load, res.value.loadAttributes);
        formMethods.reset(normalizeLoad(tmsName, res.value.load));
      }
      // If updated load not in response for some reason, re-fetch from API
      else await invalidateLoad();
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });

      if (res.error.load && res.error.loadAttributes) {
        await invalidateLoad(res.error.load, res.error.loadAttributes);
        formMethods.reset(normalizeLoad(tmsName, res.error.load));
      } else {
        await invalidateLoad();
      }
    }

    submitAcceptedSuggestion(
      reqData,
      clickedSuggestion,
      setCurrentState,
      setSuggestedFieldsPrevValues
    );

    setLoading(false);
  };

  const onInvalid: SubmitErrorHandler<Email> = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  const isNFIThreeG = tmsName === TMS.ThreeG;
  const isQuantumEdge = tmsName === TMS.QuantumEdge;

  if (isQuantumEdge) {
    return (
      <QuantumEdgeLoadForm
        isCreateMode={false}
        load={load}
        onSubmit={async (data) => {
          setLoading(true);

          const reqData = buildUpdateLoadRequest(load, data, tmsName);

          const res = await updateTMS(load.ID!, reqData);

          if (res.isOk()) {
            toast({
              description: res.value.message,
              variant: 'success',
            });

            if (res.value.load && res.value.loadAttributes) {
              await invalidateLoad(res.value.load, res.value.loadAttributes);
            } else {
              await invalidateLoad();
            }
          } else {
            toast({
              description: res.error.message,
              variant: 'destructive',
            });

            if (res.error.load && res.error.loadAttributes) {
              await invalidateLoad(res.error.load, res.error.loadAttributes);
            } else {
              await invalidateLoad();
            }
          }

          setLoading(false);
        }}
        onSuccess={() => {}}
        onError={() => {}}
      />
    );
  }

  return (
    <div className='mb-5'>
      {showScrollUntilComponent &&
        clickedSuggestion?.pipeline === SuggestionPipelines.CarrierInfo && (
          <ScrollUntilComponent />
        )}

      {/* Mcleod Load Info leverages Antd Select components to enable optimized fuzzy search, but they don't support highlighting dirty fields.
  So for the sake of making all the input fields consistent, we're disabling highlighting for Mcleod */}
      <ExtendedFormProvider
        aiDefaultValues={false}
        highlightDirtyFields={tmsName !== TMS.McleodEnterprise}
        onFieldChange={handleFieldChange}
      >
        <FormProvider {...formMethods}>
          <form onSubmit={handleSubmit(onSubmit, onInvalid)}>
            <Accordion
              type='multiple'
              value={activeTabs}
              onValueChange={setActiveTabs}
            >
              {!isUnsupportedSection(allFieldAttrs, 'customer') && (
                <LoadSectionAccordionItem
                  label='Customer'
                  icon={<Building2 className='h-6 w-6' strokeWidth={1} />}
                  name='customer'
                  activeTabs={activeTabs}
                >
                  <CustomerSectionForm
                    customers={customers}
                    setCustomers={setCustomers}
                    isLoadingCustomers={isLoadingCustomers}
                    setIsLoadingCustomers={setIsLoadingCustomers}
                    formMethods={formMethods}
                  />
                </LoadSectionAccordionItem>
              )}

              {!isUnsupportedSection(allFieldAttrs, 'billTo') && (
                <LoadSectionAccordionItem
                  label='Bill To'
                  icon={<ReceiptIcon className='h-6 w-6' strokeWidth={1} />}
                  name='billTo'
                  activeTabs={activeTabs}
                >
                  <BillToSectionForm />
                </LoadSectionAccordionItem>
              )}

              {!isUnsupportedSection(allFieldAttrs, 'specifications') && (
                <LoadSectionAccordionItem
                  label='Specs'
                  icon={<Weight className='h-6 w-6' strokeWidth={1} />}
                  name='specifications'
                  activeTabs={activeTabs}
                >
                  <SpecificationsForm load={load} formMethods={formMethods} />
                </LoadSectionAccordionItem>
              )}

              {!isUnsupportedSection(allFieldAttrs, 'rateData') &&
                tmsName !== TMS.Aljex && (
                  <LoadSectionAccordionItem
                    label='Rates'
                    icon={
                      <CircleDollarSignIcon
                        className='h-6 w-6'
                        strokeWidth={1}
                      />
                    }
                    name='rateData'
                    activeTabs={activeTabs}
                  >
                    <RatesForm
                      formMethods={formMethods}
                      showCarrierFields={true}
                    />
                  </LoadSectionAccordionItem>
                )}

              {isMultiStopLoadViewEnabled &&
              load.stops &&
              load.stops.length > 2 ? (
                <LoadSectionAccordionItem
                  label='Route'
                  icon={<MapPinnedIcon className='h-6 w-6' strokeWidth={1} />}
                  name='route'
                  activeTabs={activeTabs}
                >
                  <RouteSectionForm
                    stops={load.stops}
                    formMethods={formMethods}
                  />
                </LoadSectionAccordionItem>
              ) : (
                <>
                  <LoadSectionAccordionItem
                    label='Pickup'
                    icon={<BoxIcon className='h-6 w-6' strokeWidth={1} />}
                    name='pickup'
                    activeTabs={activeTabs}
                  >
                    <PickupSectionForm
                      formMethods={formMethods}
                      isLoadingLocations={isLoadingLocations}
                      locations={locations}
                      handleRefreshLocations={handleRefreshLocations}
                      setLocations={setLocations}
                      suggestedFieldsPrevValues={suggestedFieldsPrevValues}
                    />
                  </LoadSectionAccordionItem>

                  <LoadSectionAccordionItem
                    label='Consignee'
                    icon={<WarehouseIcon className='h-6 w-6' strokeWidth={1} />}
                    name='consignee'
                    activeTabs={activeTabs}
                  >
                    <ConsigneeSectionForm
                      formMethods={formMethods}
                      isLoadingLocations={isLoadingLocations}
                      locations={locations}
                      handleRefreshLocations={handleRefreshLocations}
                      setLocations={setLocations}
                      suggestedFieldsPrevValues={suggestedFieldsPrevValues}
                    />
                  </LoadSectionAccordionItem>
                </>
              )}

              <LoadSectionAccordionItem
                label='Carrier'
                icon={<TruckIcon className='h-6 w-6' strokeWidth={1} />}
                name='carrier'
                activeTabs={activeTabs}
              >
                {isTurvoSectionLinksEnabled &&
                  externalLinks &&
                  externalLinks.viewCarrier && (
                    <div className='p-4 bg-neutral-200 rounded'>
                      <a
                        href={externalLinks.viewCarrier}
                        target='_blank'
                        rel='noreferrer'
                        className='underline text-info flex flex-row items-center'
                      >
                        Edit carrier details on Turvo.{' '}
                        <ExternalLinkIcon className='w-3 h-3 ml-1' />
                      </a>
                    </div>
                  )}
                <CarrierSectionForm
                  load={load}
                  suggestedFieldsPrevValues={suggestedFieldsPrevValues}
                  formMethods={formMethods}
                  isLoadingCarriers={isLoadingCarriers}
                  carriers={carriers}
                  setIsLoadingCarriers={setIsLoadingCarriers}
                  setCarriers={setCarriers}
                  setIsCarrierQualified={setCarrierIsQualified}
                  hideCarrierTimeInputs={isNFIThreeG}
                />
              </LoadSectionAccordionItem>

              {isOperatorEnabled && !isNFIThreeG && (
                <LoadSectionAccordionItem
                  label='Operator'
                  icon={<CircleUserRound className='h-6 w-6' strokeWidth={1} />}
                  name='operator'
                  activeTabs={activeTabs}
                >
                  <OperatorSectionForm
                    formMethods={formMethods}
                    operators={operators}
                    canEdit={
                      !fieldAttributes.find((obj) => 'operator' in obj)
                        ?.isReadOnly
                    }
                  />
                </LoadSectionAccordionItem>
              )}

              {tmsName === TMS.GlobalTranzTMS && load.notes && (
                <LoadSectionAccordionItem
                  label='Notes'
                  icon={<NotebookIcon className='h-6 w-6' strokeWidth={1} />}
                  name='notes'
                  activeTabs={activeTabs}
                >
                  <NotesSectionForm notes={load.notes} />
                </LoadSectionAccordionItem>
              )}
            </Accordion>

            {load.moreThanTwoStops &&
              (!load.stops || load.stops?.length === 0) && (
                <span className='flex flex-row bg-info-200 rounded-lg space-x-1 py-1 mt-3'>
                  <div>
                    <Info
                      className='h-4 w-4 pl-1'
                      color='#666666'
                      strokeWidth={3}
                    />
                  </div>
                  <span className='text-xs text-neutral-600 font-medium'>
                    {`Only the first pickup and last dropoff are shown for multi-stop loads.
                More support for multi-stop loads coming soon!`}
                  </span>
                </span>
              )}
            <section className='w-full mt-8'>
              <Button
                buttonNamePosthog={ButtonNamePosthog.UpdateTMS}
                type='submit'
                className='w-full'
                disabled={loading}
                logProperties={{
                  loadID: load.ID,
                  freightTrackingID: load.freightTrackingID,
                  serviceID: load.serviceID,
                }}
              >
                {loading ? <ButtonLoader /> : ButtonText.UpdateTMS}
              </Button>
            </section>
          </form>
        </FormProvider>
      </ExtendedFormProvider>
    </div>
  );
}

const submitAcceptedSuggestion = async (
  reqData: { operation: TMSOperation; load: Load },
  clickedSuggestion: Maybe<GenericSuggestion>,
  setCurrentState: React.Dispatch<React.SetStateAction<SidebarState>>,
  setSuggestedFieldsPrevValues: React.Dispatch<
    React.SetStateAction<CarrierChanges | undefined>
  >
): Promise<void> => {
  if (!clickedSuggestion) return;

  const isCarrierSuggestion =
    clickedSuggestion?.pipeline === SuggestionPipelines.CarrierInfo;
  const isApptSuggestion =
    clickedSuggestion?.pipeline === SuggestionPipelines.ApptConfirmation;

  if (isCarrierSuggestion || isApptSuggestion) {
    const suggestedFields = clickedSuggestion?.suggested as CarrierChanges;
    const clickedSuggestionChanges = Object.entries(suggestedFields)
      .map((sug) => {
        const sugField = isCarrierSuggestion
          ? (sug[0] as keyof CarrierChanges)
          : (sug[0] as keyof ApptConfirmationChanges);

        let sugFieldValue;
        if (isApptSuggestion) {
          switch (sugField) {
            case 'pickupApptTime':
              sugFieldValue = reqData.load.pickup.apptStartTime;
              break;
            case 'dropoffApptTime':
              sugFieldValue = reqData.load.consignee.apptStartTime;
              break;
          }
        }
        return {
          [sugField]: isCarrierSuggestion
            ? reqData.load.carrier[sugField as keyof CarrierChanges]
            : sugFieldValue,
        };
      })
      .reduce((r, c) => Object.assign(r, c), {}) as CarrierChanges;

    await applyLoadSuggestion(clickedSuggestion?.id, {
      existingLoadSuggestion: clickedSuggestionChanges,
    });

    // Remove the applied suggestion from the list of suggestions
    setCurrentState((prevState) => ({
      ...prevState,
      clickedSuggestion: null,
      curSuggestionList: prevState.curSuggestionList.filter(
        (s) => s.id !== clickedSuggestion?.id
      ),
    }));

    setSuggestedFieldsPrevValues(undefined);
  }
};

const getLoadInfoWithSuggestion = (
  normalizedLoad: NormalizedLoad,
  suggestedFields: LoadChanges
): NormalizedLoad => ({
  ...normalizedLoad,
  carrier: {
    ...normalizedLoad.carrier,

    // If there's a clicked suggestion in state, we check the if fields below exists on it.
    firstDriverName:
      suggestedFields?.firstDriverName ??
      normalizedLoad.carrier.firstDriverName,
    firstDriverPhone: suggestedFields?.firstDriverPhone
      ? FormatPhoneNumber(suggestedFields?.firstDriverPhone)
      : normalizedLoad.carrier.firstDriverPhone,
    secondDriverName:
      suggestedFields?.secondDriverName ??
      normalizedLoad.carrier.secondDriverName,
    secondDriverPhone: suggestedFields?.secondDriverPhone
      ? FormatPhoneNumber(suggestedFields?.secondDriverPhone)
      : normalizedLoad.carrier.secondDriverPhone,
    truckNumber:
      suggestedFields?.truckNumber ?? normalizedLoad.carrier.truckNumber,
    trailerNumber:
      suggestedFields?.trailerNumber ?? normalizedLoad.carrier.trailerNumber,
    dispatchedTime:
      suggestedFields?.dispatchedTime ?? normalizedLoad.carrier.dispatchedTime,
    dispatchSource:
      suggestedFields?.dispatchSource ?? normalizedLoad.carrier.dispatchSource,
    expectedPickupTime:
      suggestedFields?.expectedPickupTime ??
      normalizedLoad.carrier.expectedPickupTime,
  },
  pickup: {
    ...normalizedLoad.pickup,
    // If there's a clicked suggestion in state, we check the if fields below exists on it.
    apptStartTime: suggestedFields?.pickupApptTime
      ? normalizeDateForTMS(new Date(suggestedFields.pickupApptTime))
      : normalizedLoad.pickup.apptStartTime,
  },
  consignee: {
    ...normalizedLoad.consignee,
    // If there's a clicked suggestion in state, we check the if fields below exists on it.
    apptStartTime: suggestedFields?.dropoffApptTime
      ? normalizeDateForTMS(new Date(suggestedFields.dropoffApptTime))
      : normalizedLoad.consignee.apptStartTime,
  },
});

export function buildUpdateLoadRequest(
  load: Load,
  data: NormalizedLoad,
  tmsName: string
) {
  return {
    operation: TMSOperation.UpdateLoad,
    load: {
      // Ensure we're sending a full load
      ...load,
      freightTrackingID: data.freightTrackingID,
      externalTMSID: data.externalTMSID,
      operator: data.operator,
      customer: denormalizeDatesForTMSForm(tmsName, data.customer),
      billTo: denormalizeDatesForTMSForm(tmsName, data.billTo),
      specifications: denormalizeDatesForTMSForm(tmsName, data.specifications),
      pickup: {
        ...denormalizeDatesForTMSForm(tmsName, data.pickup),
        externalTMSID: data.pickup.externalTMSID ?? data.pickup.name,
      },
      consignee: {
        ...denormalizeDatesForTMSForm(tmsName, data.consignee),
        externalTMSID: data.consignee.externalTMSID ?? data.consignee.name,
      },
      carrier: {
        ...denormalizeDatesForTMSForm(tmsName, data.carrier),
        rateConfirmationSent: data.carrier.rateConfirmationSent ? true : false,
      },
      rateData: denormalizeDatesForTMSForm(tmsName, data.rateData),
      additionalReferences: data.additionalReferences || [],
      stops: (data.stops || []).map((stop) => ({
        ...stop,
        ...denormalizeDatesForTMSForm(tmsName, stop),
      })),
    } as Load,
  };
}
