import { <PERSON><PERSON><PERSON>, UseFormSetError } from 'react-hook-form';

import _ from 'lodash';

import {
  DATTooltipConstructor,
  GreenscreensTooltipConstructor,
  LaneHistoryTooltipConstructor,
  PriceRangeType,
  QuoteCardType,
} from 'components/QuoteCard';
import { CANADA_PROVINCE_ABBREVIATIONS } from 'constants/CanadaProvinceTimezones';
import { US_STATE_ABBREVIATIONS } from 'constants/USStateTimezones';
import { toast } from 'hooks/useToaster';
import AljexLogo from 'icons/Aljex';
import DATLogo from 'icons/DAT';
import GlobalTranzLogo from 'icons/GlobalTranz';
import <PERSON><PERSON><PERSON><PERSON>ogo from 'icons/McleodEnterprise';
import TrifectaLogo from 'icons/TrifectaLogo';
import TriumphGSLogo from 'icons/TriumphGSLogo';
import TruckstopLogo from 'icons/TruckstopLogo';
import TurvoLogo from 'icons/Turvo';
import { enableDATIndividualAccess } from 'lib/api/enableDATIndividualAccess';
import { getCustomers } from 'lib/api/getCustomers';
import { DATLaneHistoryEntry } from 'lib/api/getDATLaneHistory';
import { LaneHistoryResponse, getLaneHistory } from 'lib/api/getLaneHistory';
import { getLaneHistoryFromService } from 'lib/api/getLaneHistoryFromService';
import { getLaneRateFromService } from 'lib/api/getLaneRateFromService';
import {
  QuickQuoteResponseQuote,
  QuickQuoteResponseStop,
} from 'lib/api/getQuickQuote';
import { getQuoteNumber } from 'lib/api/getQuoteNumber';
import {
  GreenscreensQuote,
  sendGreenscreensQuoteToService,
} from 'lib/api/postGreenscreensQuoteToService';
import {
  UserQuote,
  sendUserQuoteToService,
} from 'lib/api/postUserQuoteToService';
import { submitQuoteToTMS } from 'lib/api/submitQuoteToTMS';
import { submitQuoteViaURL } from 'lib/api/submitQuoteViaURL';
import { updateQuoteRequestSuggestion } from 'lib/api/updateQuoteRequestSuggestion';
import {
  QuoteCountries,
  QuoteFormStop,
  SelectedQuoteType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { Quoting, TMS } from 'types/enums/Integrations';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import { captureResponseInPosthog } from 'utils/captureResponseInPosthog';
import { calculatePricing } from 'utils/priceCalculations';

import { DATLaneHistoryChartEntry } from './DATLaneHistory';
import {
  DATQuoteLocationType,
  DATQuoteTimeframe,
  FetchCustomersProps,
  FetchLaneRateFromServiceProps,
  FetchQuoteNumberProps,
  HandleQuoteSubmissionViaURLProps,
  HelperFunctions,
  OnSubmitFormProps,
  ParseQuoteCardsFromResponseProps,
  ProcessQuoteTMSSubmissionProps,
  QuickQuoteInputs,
  QuoteTypeInSource,
  SendGreenscreensQuoteProps,
  SendUserQuoteProps,
} from './types';

// Sort DAT quote cards to appear first in list of QuoteCards
const sortDATCardsFirst = (cards: QuoteCardType[]): QuoteCardType[] => {
  const isDATType = (card: QuoteCardType) =>
    card.type === SelectedQuoteType.DAT_RATEVIEW ||
    card.type === SelectedQuoteType.DAT_LONGEST_LEG ||
    card.type === SelectedQuoteType.DAT_LEG_TO_LEG;

  const datCards = cards.filter(isDATType);
  const otherCards = cards.filter((c) => !isDATType(c));
  return [...datCards, ...otherCards];
};

export const formatStopsDisplay = (stops: QuickQuoteResponseStop[]) => {
  const getLocationString = (stop: QuickQuoteResponseStop) => {
    const hasCity = Boolean(stop.city && stop.city.trim());
    const hasState = Boolean(stop.state && stop.state.trim());
    if (hasCity && hasState) {
      return `${useHelperFunctions.toTitleCase(stop.city)}, ${stop.state}`;
    }
    // Fallbacks for ZIP-to-ZIP or partial data
    if (stop.zip && String(stop.zip).trim()) {
      return String(stop.zip).trim();
    }
    if (hasCity) {
      return useHelperFunctions.toTitleCase(stop.city);
    }
    if (hasState) {
      return stop.state;
    }
    return '';
  };

  if (!stops || stops.length === 0) return '';
  if (stops.length <= 2) {
    return `${getLocationString(stops[0])} to ${getLocationString(stops[stops.length - 1])}`;
  }

  const middleStops = stops.slice(1, -1).map(getLocationString).filter(Boolean);

  if (middleStops.length === 0) {
    return `${getLocationString(stops[0])} to ${getLocationString(stops[stops.length - 1])}`;
  }

  if (middleStops.length === 1) {
    return `${getLocationString(stops[0])} through ${middleStops[0]} to ${getLocationString(stops[stops.length - 1])}`;
  }

  const lastMiddleStop = middleStops[middleStops.length - 1];
  const restStops = middleStops.slice(0, -1);
  return `${getLocationString(stops[0])} through ${restStops.join(', ')} and ${lastMiddleStop} to ${getLocationString(stops[stops.length - 1])}`;
};

export const useHelperFunctions: HelperFunctions = {
  getQuoteCards: ({ quotes, updatedFormValues, isMultiStop }) => {
    const mapSelectedType = (qType: QuoteTypeInSource): SelectedQuoteType => {
      switch (qType) {
        case QuoteTypeInSource.DAT_LONGEST_LEG:
          return SelectedQuoteType.DAT_LONGEST_LEG;
        case QuoteTypeInSource.DAT_LEG_TO_LEG:
          return SelectedQuoteType.DAT_LEG_TO_LEG;
        default:
          return SelectedQuoteType.DAT_RATEVIEW;
      }
    };

    const mapSubtitle = (qType: QuoteTypeInSource): string => {
      switch (qType) {
        case QuoteTypeInSource.DAT_LONGEST_LEG:
          return 'Longest Leg';
        case QuoteTypeInSource.DAT_LEG_TO_LEG:
          return 'Leg-to-Leg';
        default:
          return '';
      }
    };

    return quotes
      .filter((q) => q.source === Quoting.DAT)
      .map((quote) => {
        const qType = quote.type as QuoteTypeInSource;
        return {
          type: mapSelectedType(qType),
          title: 'RateView',
          subtitle: mapSubtitle(qType),
          icon: <DATLogo width={64} className='inline-block' />,
          cost: quote.rates.target,
          costPerMile: quote.rates.targetPerMile,
          priceRange: {
            lowEstimate: quote.rates.low,
            highEstimate: quote.rates.high,
          } as PriceRangeType,
          confidence: null,
          priceRangePerMile: {
            lowEstimate: quote.rates.lowPerMile,
            highEstimate: quote.rates.highPerMile,
          } as PriceRangeType,
          tooltipContent: {
            typeInSource: qType,
            isMultiStop,
            stops: updatedFormValues.stops,
            timeframe: quote.metadata?.timeframe || '',
            originName: quote.metadata?.originName || '',
            originType:
              quote.metadata?.originType || DATQuoteLocationType.REGION,
            destinationName: quote.metadata?.destinationName || '',
            destinationType:
              quote.metadata?.destinationType || DATQuoteLocationType.REGION,
            reports: quote.metadata?.reports || 0,
            companies: quote.metadata?.companies || 0,
            stopFeeUSDMedium: quote.metadata?.stopFeeUSDMedium || undefined,
          },
          tooltipConstructor: DATTooltipConstructor,
          inputtedTransportType: updatedFormValues.transportType,
          actualTransportType: updatedFormValues.transportType,
          quotingIntegrationName: SelectedQuoteType.DAT_RATEVIEW,
          isMultiStop,
          legs: quote.metadata?.legs || undefined,
        } as QuoteCardType;
      });
  },
  toTitleCase: (str: string): string => {
    return str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
  },

  sendGreenscreensQuote: async ({
    quote,
    setGreenscreensQuoteID,
  }: SendGreenscreensQuoteProps) => {
    const gsNetworkQuote = quote.quotes.find(
      (q) => q.type === QuoteTypeInSource.GS_Network
    );
    const gsBuyPowerQuote = quote.quotes.find(
      (q) => q.type === QuoteTypeInSource.GS_BuyPower
    );

    const greenscreensQuoteObject: GreenscreensQuote = {
      quoteRequestId: quote.quoteRequestId,
      stops: quote.stops as QuickQuoteResponseStop[], // TODO: Double-check this
      selectedRateName: quote.selectedRateName,
      networkLaneRateDistance: gsNetworkQuote?.distance || 0,
      networkLaneRateTargetBuy: gsNetworkQuote?.rates.targetPerMile || 0,
      networkLaneRateConfidenceLevel:
        gsNetworkQuote?.metadata?.confidenceLevel || 0,
      laneRateDistance: gsBuyPowerQuote?.distance || 0,
      laneRateTargetBuy: gsBuyPowerQuote?.rates.targetPerMile || 0,
      laneRateConfidenceLevel: gsBuyPowerQuote?.metadata?.confidenceLevel || 0,
    };
    const res = await sendGreenscreensQuoteToService(greenscreensQuoteObject);
    if (res.isOk()) {
      setGreenscreensQuoteID(res.value);
    }
  },

  sendUserQuote: async ({
    email,
    quoteResponse,
    parentQuoteRequestId,
    greenscreensQuoteID,
    carrierCost,
    profit,
    profitType,
    finalPrice,
    draftResponse,
  }: SendUserQuoteProps) => {
    if (!greenscreensQuoteID || !email || !quoteResponse) return;

    const roundedFinalPrice = _.round(finalPrice);
    // TODO: Remove marginType and use profitType instead
    const userQuoteObject: UserQuote = {
      quoteRequestId: parentQuoteRequestId,
      gsQuoteID: greenscreensQuoteID,
      draftResponse: draftResponse,
      carrierCost: carrierCost,
      margin: profit,
      marginType: profitType,
      targetSell: roundedFinalPrice,
      stops: quoteResponse.stops as QuickQuoteResponseStop[], // TODO: Double-check this
    };

    await sendUserQuoteToService(userQuoteObject);
  },

  fetchQuoteNumber: async ({
    email,
    setHasThirdPartyQuoteURLs,
    setValue,
  }: FetchQuoteNumberProps) => {
    if (!email) return;
    const res = await getQuoteNumber(email.id);
    if (res.isOk()) {
      setHasThirdPartyQuoteURLs(res.value.hasThirdPartyQuoteURLs);
      // can't use resetField here because the form input hasn't been initialized yet
      setValue('quoteNumber', res.value.quoteNumber);
      return;
    }
    setHasThirdPartyQuoteURLs(false);
  },

  fetchLaneRateFromService: async ({
    emailId,
    threadId,
    quoteRequestId,
    updatedFormValues,
  }: FetchLaneRateFromServiceProps) => {
    const res = await getLaneRateFromService({
      emailId: emailId,
      threadId: threadId,
      quoteRequestId: quoteRequestId,
      transportType: updatedFormValues.transportType,
      originDate: updatedFormValues.pickupDate
        ? new Date(updatedFormValues.pickupDate).toISOString()
        : new Date(0).toISOString(),
      destinationDate: updatedFormValues.deliveryDate
        ? new Date(updatedFormValues.deliveryDate).toISOString()
        : new Date(0).toISOString(),
      // Multi-stop support: pass all stops so backend can handle 2-stop or multi-stop uniformly
      stops: updatedFormValues.stops.map((stop, index) => ({
        order: index,
        city: stop.city,
        state: stop.state,
        zip: stop.zip,
        country: stop.country || '',
      })),
    });

    if (res.isOk()) {
      const {
        lowPerTrip,
        highPerTrip,
        lowPerMile,
        highPerMile,
        timeframe,
        companies,
        reports,
        originName,
        originType,
        destinationName,
        destinationType,
        quotes,
      } = res.value;

      const hasQuotes = Array.isArray(quotes) && quotes.length > 0;
      const cards: QuoteCardType[] = [];

      if (hasQuotes) {
        const isMultiStop = updatedFormValues.stops.length > 2;
        const quoteCards = useHelperFunctions.getQuoteCards({
          quotes: quotes as QuickQuoteResponseQuote[],
          updatedFormValues,
          isMultiStop,
        });

        if (quoteCards.length) {
          cards.push(...quoteCards);
        }
      } else {
        cards.push({
          type: SelectedQuoteType.DAT_RATEVIEW,
          title: `RateView Median`,
          icon: <DATLogo className='inline-block w-auto h-3' />,
          cost: Number(res.value.ratePerTrip),
          costPerMile: Number(res.value.ratePerMile),
          confidence: null,
          priceRange:
            lowPerTrip && highPerTrip
              ? ({
                  lowEstimate: lowPerTrip,
                  highEstimate: highPerTrip,
                } as PriceRangeType)
              : null,
          priceRangePerMile:
            lowPerMile && highPerMile
              ? ({
                  lowEstimate: lowPerMile,
                  highEstimate: highPerMile,
                } as PriceRangeType)
              : null,
          tooltipContent: {
            typeInSource: QuoteTypeInSource.TMSLaneHistory,
            isMultiStop: updatedFormValues.stops.length > 2,
            stops: updatedFormValues.stops,
            timeframe: DATQuoteTimeframe[timeframe],
            originName: originName,
            originType: DATQuoteLocationType[originType],
            destinationName: destinationName,
            destinationType: DATQuoteLocationType[destinationType],
            companies,
            reports,
          },
          tooltipConstructor: DATTooltipConstructor,
          inputtedTransportType: updatedFormValues.transportType,
          actualTransportType: updatedFormValues.transportType,
          isMultiStop: false,
          stops: convertFormStopstoResponseStops(updatedFormValues.stops),
        });
      }

      return cards;
    }

    let serviceLaneRateError =
      'Oops, something went wrong while fetching tailored lane rates. Showing standard lane rates instead.';

    if (res.error.message.includes("Trident's DAT integration")) {
      serviceLaneRateError =
        'Trident was unable to retrieve lane rate from DAT. Showing standard lane rates instead.';
      toast({
        description: serviceLaneRateError,
        variant: 'info',
      });
    } else if (res.error.message.includes('Unrecognized Service')) {
      serviceLaneRateError =
        'Tailored rates unavailable. Showing standard lane rates instead.';
      toast({
        description: serviceLaneRateError,
        variant: 'info',
      });
    } else {
      toast({
        description: serviceLaneRateError,
        variant: 'info',
      });
    }

    return [];
  },

  fetchLaneHistoryFromService: async ({
    quoteRequestId,
    originCity,
    originState,
    originZip,
    originCountry,
    destinationCity,
    destinationState,
    destinationZip,
    destinationCountry,
    transportType,
  }: {
    quoteRequestId: number;
    originCity: string;
    originState: string;
    originZip: string;
    originCountry: string;
    destinationCity: string;
    destinationState: string;
    destinationZip: string;
    destinationCountry: string;
    transportType: string;
  }) => {
    const res = await getLaneHistoryFromService({
      quoteRequestId,
      originCity,
      originState,
      originZip,
      originCountry,
      destinationCity,
      destinationState,
      destinationZip,
      destinationCountry,
      transportType,
    });
    return res;
  },

  fetchCustomers: async ({
    setInitialCustomers,
    setCustomers,
    setTMSTenant,
    tmsIntegrations,
  }: FetchCustomersProps) => {
    const res = await getCustomers(tmsIntegrations?.[0]?.id);
    if (res.isOk()) {
      setInitialCustomers(res.value.customerList);
      setCustomers(res.value.customerList);
      setTMSTenant(res.value.tmsTenant);
    } else {
      toast({
        description: 'Error while fetching customer list.',
        variant: 'destructive',
      });
    }
  },

  parseQuoteCardsFromResponse: ({
    newQuote,
    formValues,
    setDATFuelSurcharge,
  }: ParseQuoteCardsFromResponseProps) => {
    const newQuoteCards: QuoteCardType[] = [];
    if (!newQuote?.quotes?.length) {
      return newQuoteCards;
    }
    const isMultiStop = newQuote.stops.length > 2;

    newQuote.quotes.forEach((quote) => {
      switch (quote.source) {
        case Quoting.Greenscreens: {
          const isBuyPower = quote.type === QuoteTypeInSource.GS_BuyPower;
          const isZipCodeLookup = newQuote.isZipCodeLookup;
          newQuoteCards.push({
            type: isBuyPower
              ? SelectedQuoteType.GS_BUYPOWER
              : SelectedQuoteType.GS_NETWORK,
            title: isBuyPower ? 'Verified Buy Rate' : 'Market Rate',
            tooltipContent: {
              isMultiStop: formValues.stops.length > 2,
              stops: formValues.stops,
              typeInSource: quote.type,
              isZipCodeLookup,
            },
            tooltipConstructor: () =>
              GreenscreensTooltipConstructor(isZipCodeLookup),
            icon: (
              <TriumphGSLogo height={16} width={75} className='inline-block' />
            ),
            cost: quote.rates.targetPerMile * quote.distance,
            costPerMile: quote.rates.targetPerMile,
            confidence: quote.metadata?.confidenceLevel || null,
            priceRange: null,
            inputtedTransportType: formValues.transportType,
            actualTransportType: newQuote.submittedTransportType,
            quotingIntegrationName: useHelperFunctions.toTitleCase(
              Quoting.Greenscreens
            ),
            stops: convertFormStopstoResponseStops(formValues.stops),
          });
          break;
        }
        case Quoting.DAT: {
          if (quote.metadata?.fuelSurchargePerMile) {
            setDATFuelSurcharge(quote.metadata?.fuelSurchargePerMile);
          }

          newQuoteCards.push({
            type:
              quote.type === QuoteTypeInSource.DAT_LONGEST_LEG
                ? SelectedQuoteType.DAT_LONGEST_LEG
                : quote.type === QuoteTypeInSource.DAT_LEG_TO_LEG
                  ? SelectedQuoteType.DAT_LEG_TO_LEG
                  : SelectedQuoteType.DAT_RATEVIEW,
            title: 'RateView',
            subtitle:
              quote.type === QuoteTypeInSource.DAT_LONGEST_LEG
                ? 'Longest Leg'
                : quote.type === QuoteTypeInSource.DAT_LEG_TO_LEG
                  ? 'Leg-to-Leg'
                  : '',
            icon: <DATLogo width={64} className='inline-block' />,
            cost: quote.rates.target,
            costPerMile: quote.rates.targetPerMile,
            priceRange: {
              lowEstimate: quote.rates.low,
              highEstimate: quote.rates.high,
            } as PriceRangeType,
            confidence: null,
            priceRangePerMile: {
              lowEstimate: quote.rates.lowPerMile,
              highEstimate: quote.rates.highPerMile,
            } as PriceRangeType,
            tooltipContent: {
              typeInSource: quote.type,
              isMultiStop,
              stops: formValues.stops,
              timeframe: quote.metadata?.timeframe || '',
              originName: quote.metadata?.originName || '',
              originType:
                quote.metadata?.originType || DATQuoteLocationType.REGION,
              destinationName: quote.metadata?.destinationName || '',
              destinationType:
                quote.metadata?.destinationType || DATQuoteLocationType.REGION,
              reports: quote.metadata?.reports || 0,
              companies: quote.metadata?.companies || 0,
              stopFeeUSDMedium: quote.metadata?.stopFeeUSDMedium || undefined,
            },
            tooltipConstructor: DATTooltipConstructor,
            inputtedTransportType: formValues.transportType,
            actualTransportType: newQuote.submittedTransportType,
            quotingIntegrationName: SelectedQuoteType.DAT_RATEVIEW,
            isMultiStop,
            stops: convertFormStopstoResponseStops(formValues.stops),
            legs: quote.metadata?.legs || undefined,
          });
          break;
        }
        case Quoting.TruckStop: {
          const isTruckstopBooked =
            quote.type === QuoteTypeInSource.TruckStop_Booked;
          newQuoteCards.push({
            type: isTruckstopBooked
              ? SelectedQuoteType.TRUCKSTOP_BOOKED
              : SelectedQuoteType.TRUCKSTOP_POSTED,
            title: isTruckstopBooked ? 'Booked Quote' : 'Posted Quote',
            icon: (
              <TruckstopLogo
                width={30}
                className='inline-block'
                fill='#D90119'
              />
            ),
            cost: quote.rates.target,
            costPerMile: quote.rates.targetPerMile,
            confidence: quote.metadata?.confidenceLevel || null,
            priceRange: {
              lowEstimate: quote.rates.low,
              highEstimate: quote.rates.high,
            } as PriceRangeType,
            inputtedTransportType: formValues.transportType,
            actualTransportType: newQuote.submittedTransportType,
            quotingIntegrationName: useHelperFunctions.toTitleCase(
              Quoting.TruckStop
            ),
            isMultiStop,
            stops: convertFormStopstoResponseStops(formValues.stops),
            legs: quote.metadata?.legs || undefined,
          });
        }
      }
    });
    return newQuoteCards;
  },

  // Internal helper function for validating stops
  validateStops: (
    formValues: QuickQuoteInputs,
    setError: UseFormSetError<QuickQuoteInputs>
  ): Array<[FieldPath<QuickQuoteInputs>, string]> => {
    // Validate all stops
    const validationErrors: Array<[FieldPath<QuickQuoteInputs>, string]> = [];

    formValues.stops.forEach((stop, index) => {
      const parsedLocation = useHelperFunctions.parseLocation(
        stop.location || ''
      );
      if (!parsedLocation) {
        validationErrors.push([
          `stops.${index}.location`,
          'Please enter valid zip code or City, State',
        ]);
      }
    });

    if (validationErrors.length) {
      validationErrors.forEach(([field, message]) =>
        setError(field as FieldPath<QuickQuoteInputs>, { message })
      );
    }
    return validationErrors;
  },

  onSubmitForm: async ({
    isMultiStopQuickQuoteEnabled,
    formValues,
    setIsSubmitToTMS,
    setCreatedQuoteId,
    setQuoteResponse,
    setQuoteCards,
    setQuoteCardErrors,
    isQuoteSubmissionViaURLEnabled,
    email,
    setHasThirdPartyQuoteURLs,
    setValue,
    isGetLaneRateFromServiceEnabled,
    clickedSuggestion,
    formMethods,
    setQuoteNotConfident,
    getQuickQuote,
    isQuoteLaneHistoryEnabled,
    isTMSLaneHistoryEnabled,
    setIsLoadingLaneHistory,
    setLaneHistory,
    setCarrierCost,
    setProfit,
    profitType,
    setError,
    setParentQuoteRequestId,
    setGreenscreensQuoteID,
    isQuoteSubmissionToServiceEnabled,
    setDATFuelSurcharge,
    selectedQuickQuoteId,
    serviceID,
    posthog,
  }: OnSubmitFormProps) => {
    // these state variables should be reset when the form is submitted
    setIsSubmitToTMS(true);
    setCreatedQuoteId(null);
    setQuoteResponse(null);
    setQuoteCards([]);
    setQuoteCardErrors([]);
    setCarrierCost(0);
    setLaneHistory(null);
    setDATFuelSurcharge(null);

    const lastIndex = formValues.stops.length - 1;

    const emptyStops = formValues.stops.filter((stop) => !stop.location);
    if (emptyStops.length > 0) {
      toast({
        description:
          'Please enter locations for all stops before getting a quote.',
        variant: 'destructive',
      });
      return;
    }

    const pickupDate = new Date(formValues.pickupDate);

    // Only validate pickup date exists
    if (!pickupDate) {
      toast({
        description: 'Please enter a pickup date.',
        variant: 'destructive',
      });
      return;
    }

    // Validate all stops
    const validationErrors: Array<[string, string]> = [];

    formValues.stops.forEach((stop, index) => {
      const parsedLocation = useHelperFunctions.parseLocation(
        stop.location || ''
      );
      if (!parsedLocation) {
        validationErrors.push([
          `stops.${index}.location`,
          'Please enter valid zip code or City, State',
        ]);
      }
    });

    if (validationErrors.length) {
      validationErrors.forEach(([field, message]) =>
        setError(field as FieldPath<QuickQuoteInputs>, { message })
      );
      return;
    }

    const updatedFormValues: QuickQuoteInputs = {
      ...formValues,
      stops: formValues.stops.map((stop, index) => {
        const parsedLocation = useHelperFunctions.parseLocation(
          stop.location || ''
        );
        if (!parsedLocation) {
          setError(`stops.${index}.location` as FieldPath<QuickQuoteInputs>, {
            message: 'Please enter valid ZIP code or City, State',
          });
          return stop;
        }

        return {
          ...stop,
          zip: parsedLocation.zip,
          city: parsedLocation.city,
          state: parsedLocation.state,
          country: parsedLocation.country,
        };
      }),
    };

    // Only need to fetch quote number when submitting quotes via URL is supported
    if (isQuoteSubmissionViaURLEnabled) {
      useHelperFunctions.fetchQuoteNumber({
        email,
        setHasThirdPartyQuoteURLs,
        setValue,
      });
    }

    const newQuote = await getQuickQuote({
      isMultiStopQuickQuoteEnabled,
      email,
      clickedSuggestion,
      formValues: updatedFormValues,
      formMethods,
      setQuoteNotConfidentHandler: setQuoteNotConfident,
      setProfit,
      profitType,
      ...(selectedQuickQuoteId && { selectedQuickQuoteId }),
    });

    captureResponseInPosthog(posthog, !newQuote, {
      serviceID,
      submissionType: ButtonNamePosthog.GetQuickQuote,
    });

    // We want general information about the quote, even if no quote cards are generated.
    if (!newQuote) return;

    // At this point, there is a parent quote request so we should set it's ID.
    // This is used to update the parent quote request to accepted at the end of the quick quote flow.
    setParentQuoteRequestId(newQuote.quoteRequestId || 0);

    // Set quote details other than quote cards
    setQuoteResponse(newQuote);

    // Build cards from GetQuickQuote response (sync)
    const quickQuoteCards: QuoteCardType[] = newQuote.quotes?.length
      ? useHelperFunctions.parseQuoteCardsFromResponse({
          newQuote,
          formValues: updatedFormValues,
          setDATFuelSurcharge,
        })
      : [];

    /// Lane Rate from Service (e.g. Trident Olympus)
    let serviceLaneRateCards: QuoteCardType[] = [];
    if (isGetLaneRateFromServiceEnabled) {
      serviceLaneRateCards = await useHelperFunctions.fetchLaneRateFromService({
        emailId: email?.id ?? 0,
        threadId: email?.threadID ?? '',
        quoteRequestId: newQuote?.quoteRequestId || 0,
        updatedFormValues,
      });
    }

    // Combine and set cards once, with DAT-first ordering
    const combinedCards = [...quickQuoteCards, ...serviceLaneRateCards];
    if (combinedCards.length) {
      setQuoteCards(sortDATCardsFirst(combinedCards));
    }

    // Deduplicate quote card errors and set in state
    const deduplicatedQuoteCardErrors = [
      ...new Set(
        newQuote.quoteErrors?.map(
          (error) => `${quoteErrorSourceMap[error.source]} - ${error.error}`
        )
      ),
    ];
    setQuoteCardErrors(deduplicatedQuoteCardErrors);

    // Handle submit-to-service logic (Greenscreens) after cards are prepared
    if (isQuoteSubmissionToServiceEnabled && newQuote.quotes?.length) {
      const greenscreensQuotes = newQuote.quotes.filter(
        (q) => q.source === Quoting.Greenscreens
      );
      if (greenscreensQuotes.length > 0) {
        await useHelperFunctions.sendGreenscreensQuote({
          quote: {
            ...newQuote,
            quotes: greenscreensQuotes,
          },
          setGreenscreensQuoteID: setGreenscreensQuoteID,
        });
      }
    }

    // Lane History (e.g. Mcleod, GlobalTranz)
    // TODO: Multi-stop lane history ENG-3759
    let laneHistoryCards: QuoteCardType[] = [];
    if (isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled) {
      setIsLoadingLaneHistory(true);

      const res = await getLaneHistory({
        quoteRequestId: newQuote?.quoteRequestId || 0,
        originCity: updatedFormValues.stops[0].city,
        originState: updatedFormValues.stops[0].state,
        originZip: updatedFormValues.stops[0].zip,
        originCountry: updatedFormValues.stops[0].country as QuoteCountries,
        destinationCity: updatedFormValues.stops[lastIndex].city,
        destinationState: updatedFormValues.stops[lastIndex].state,
        destinationZip: updatedFormValues.stops[lastIndex].zip,
        destinationCountry: updatedFormValues.stops[lastIndex]
          .country as QuoteCountries,
        transportType: updatedFormValues.transportType,
      });
      if (res.isOk()) {
        setLaneHistory(res.value);
        laneHistoryCards = useHelperFunctions.handleQuoteLaneHistory(
          res.value,
          updatedFormValues.stops
        );
      }

      setIsLoadingLaneHistory(false);
    }

    // Add lane history quote cards
    if (laneHistoryCards.length) {
      setQuoteCards((prev) => [...prev, ...laneHistoryCards]);
    }
  },

  enableDATIndividualAccess: async ({
    datEmailAddress,
    setHasGrantedDATPermissions,
  }) => {
    const res = await enableDATIndividualAccess(datEmailAddress);
    if (res.isOk()) {
      setHasGrantedDATPermissions(true);
      toast({
        description: 'Successfully enabled DAT!',
        variant: 'success',
      });
    } else {
      const lowercaseError = res.error.message.toLowerCase();
      const isConnexionError = lowercaseError.includes(
        'does not have a dat connexion seat'
      );

      const title = isConnexionError ? 'DAT reported an issue' : 'Error';
      const description = isConnexionError
        ? `Please input an email address that has a DAT Connexion seat. ` +
          `If you're unsure, please contact the DAT admin at your company.`
        : 'Failed to validate DAT credentials';

      toast({
        title: title,
        description: description,
        variant: 'destructive',
      });
    }
  },

  handleQuoteLaneHistory: (
    laneHistoryResponse: LaneHistoryResponse,
    originalStops: QuoteFormStop[]
  ): QuoteCardType[] => {
    if (!laneHistoryResponse) return [];

    const laneHistoryRespQuotes: QuoteCardType[] = [];

    // Find the first CalculatedQuote and add it to the QuoteCards
    Object.values(laneHistoryResponse.resultsBySource).forEach((source) => {
      for (const history of source) {
        if (history.calculatedQuote) {
          const quote = history.calculatedQuote;

          let quoteSourceIcon = <img />;
          let costPerMile = undefined;
          let priceRangePerMile = null;
          let type = SelectedQuoteType.LANE_HISTORY;
          switch (history.source) {
            case TMS.Aljex:
              type = SelectedQuoteType.ALJEX_LANE_HISTORY;
              quoteSourceIcon = <AljexLogo height='auto' width={60} />;
              costPerMile = quote.avgRatePerMile;
              priceRangePerMile =
                quote.minRatePerMile && quote.maxRatePerMile
                  ? ({
                      lowEstimate: quote.minRatePerMile,
                      highEstimate: quote.maxRatePerMile,
                    } as PriceRangeType)
                  : null;
              break;
            case TMS.McleodEnterprise:
              type = SelectedQuoteType.MCLEOD_LANE_HISTORY;
              quoteSourceIcon = (
                <McleodLogo width={45} className='inline-block' />
              );
              costPerMile = quote.avgRatePerMile;
              priceRangePerMile =
                quote.minCost && quote.maxCost
                  ? ({
                      lowEstimate: quote.minCost,
                      highEstimate: quote.maxCost,
                    } as PriceRangeType)
                  : null;
              break;
            case TMS.Turvo:
              type = SelectedQuoteType.TURVO_LANE_HISTORY;
              quoteSourceIcon = (
                <TurvoLogo width={45} className='inline-block' />
              );
              costPerMile = quote.avgRatePerMile;
              priceRangePerMile =
                quote.minRatePerMile && quote.maxRatePerMile
                  ? ({
                      lowEstimate: quote.minRatePerMile,
                      highEstimate: quote.maxRatePerMile,
                    } as PriceRangeType)
                  : null;
              break;
            case Quoting.GlobalTranz:
              type = SelectedQuoteType.GLOBALTRANZ_LANE_HISTORY;
              quoteSourceIcon = <GlobalTranzLogo height={12} width={75} />;
              costPerMile = quote.avgRatePerMile;
              priceRangePerMile =
                quote.minRatePerMile && quote.maxRatePerMile
                  ? ({
                      lowEstimate: quote.minRatePerMile,
                      highEstimate: quote.maxRatePerMile,
                    } as PriceRangeType)
                  : null;
              break;
            case TMS.GlobalTranzTMS:
              type = SelectedQuoteType.GLOBALTRANZ_TMS_LANE_HISTORY;
              quoteSourceIcon = <TrifectaLogo height={16} width={86} />;
              costPerMile = quote.avgRatePerMile;
              priceRangePerMile =
                quote.minRatePerMile && quote.maxRatePerMile
                  ? ({
                      lowEstimate: quote.minRatePerMile,
                      highEstimate: quote.maxRatePerMile,
                    } as PriceRangeType)
                  : null;
              break;
          }

          // TODO: Add lane tier toggle to card
          const newCard: QuoteCardType = {
            type: type,
            title: `Lane History`,
            icon: quoteSourceIcon,
            cost: quote.avgCost,
            costPerMile: costPerMile,
            confidence: null,
            priceRange:
              quote.minCost && quote.maxCost
                ? ({
                    lowEstimate: quote.minCost,
                    highEstimate: quote.maxCost,
                  } as PriceRangeType)
                : null,
            priceRangePerMile: priceRangePerMile,
            tooltipContent: {
              // We pass through the original stops to the tooltip constructor as API returns only first and last stops
              isMultiStop: originalStops.length > 2,
              stops: originalStops,
              typeInSource: QuoteTypeInSource.TMSLaneHistory,
              timeframe: history.timeframe + ' average',
              originName: 'Origin',
              originType: history.laneTier!,
              destinationName: 'Destination',
              destinationType: history.laneTier!,
            },
            tooltipConstructor: LaneHistoryTooltipConstructor,
            stops: convertFormStopstoResponseStops(originalStops),
            inputtedTransportType: history.inputtedTransportType,
            actualTransportType: history.proxiedTransportType,
            isMultiStop: false,
          };

          // This takes longer to fetch than other cards, so we're adding it to the end of the list
          // to avoid flickering/pushing user's view
          laneHistoryRespQuotes.push(newCard);

          break;
        }
      }
    });

    return laneHistoryRespQuotes;
  },

  formatDATLaneHistoryRates: (
    rates: DATLaneHistoryEntry[]
  ): DATLaneHistoryChartEntry[] => {
    return rates.map((rate) => {
      // Ensure we have valid numbers, defaulting to 0 if not
      const averageRate =
        rate.perTrip &&
        typeof rate.perTrip.rateUsd === 'number' &&
        !isNaN(rate.perTrip.rateUsd)
          ? rate.perTrip.rateUsd
          : 0;
      const maxRate =
        rate.perTrip &&
        typeof rate.perTrip.highUsd === 'number' &&
        !isNaN(rate.perTrip.highUsd)
          ? rate.perTrip.highUsd
          : 0;
      const lowestRate =
        rate.perTrip &&
        typeof rate.perTrip.lowUsd === 'number' &&
        !isNaN(rate.perTrip.lowUsd)
          ? rate.perTrip.lowUsd
          : 0;

      const date = new Date(rate.year, rate.month - 1);
      return {
        week: `${date.toLocaleString('default', { month: 'long' })} ${rate.year}`,
        averageRate,
        maxRate,
        lowestRate,
      };
    });
  },

  parseLocation: (location: string) => {
    // Check if input is a 5-digit ZIP code
    if (/^\d{5}$/.test(location.trim())) {
      return {
        country: QuoteCountries.USA,
        zip: location.trim(),
        city: '',
        state: '',
      };
    }

    // Check if input is a 6-character Canada postal code
    const canadaPostalCodeRegex =
      /^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJKLMNPRSTVWXYZ][ ]?\d[ABCEGHJKLMNPRSTVWXYZ]\d$/i;
    if (canadaPostalCodeRegex.test(location.trim())) {
      return {
        country: QuoteCountries.Canada,
        zip: location.trim(),
        city: '',
        state: '',
      };
    }

    // Parse city, state format (e.g. "Boston, MA")
    const match = location.match(/^([^,]+),\s*([A-Z]{2})$/i);
    if (match) {
      const isUSState = US_STATE_ABBREVIATIONS.includes(match![2]);
      const isCanadaProvince = CANADA_PROVINCE_ABBREVIATIONS.includes(
        match![2]
      );

      // Try matching with US state, then Canada province. If neither match, assume US state w/ typo
      return {
        country: isUSState
          ? QuoteCountries.USA
          : isCanadaProvince
            ? QuoteCountries.Canada
            : QuoteCountries.USA,
        zip: '',
        city: match[1].trim(),
        state: match[2].toUpperCase(),
      };
    }

    return null;
  },

  // TODO: Refactor for multi-stop ENG-4013
  processQuoteTMSSubmission: async ({
    customerId,
    finalPrice,
    getValues,
    setError,
  }: ProcessQuoteTMSSubmissionProps) => {
    const formValues = getValues();
    if (formValues.stops.length > 2) {
      toast({
        description:
          'Multi-stop loads are not yet supported for TMS submission.',
        variant: 'destructive',
      });
      return;
    }

    const validationErrors = useHelperFunctions.validateStops(
      formValues,
      setError
    );
    if (validationErrors.length > 0) {
      toast({
        description:
          'Unable to submit quote to TMS due to invalid location(s).',
        variant: 'destructive',
      });
      return;
    }

    const pickup = useHelperFunctions.parseLocation(
      formValues.stops[0].location || ''
    );
    const delivery = useHelperFunctions.parseLocation(
      formValues.stops[formValues.stops.length - 1].location || ''
    );

    const res = await submitQuoteToTMS({
      customerId: customerId.toString(),
      quotePrice: _.round(finalPrice ?? 0),
      transportType: getValues('transportType'),
      pickupLocationZip: pickup!.zip,
      pickupLocationCity: pickup!.city,
      pickupLocationState: pickup!.state,
      pickupDate: new Date(getValues('pickupDate'))?.toISOString() ?? '',
      deliveryLocationZip: delivery!.zip,
      deliveryLocationCity: delivery!.city,
      deliveryLocationState: delivery!.state,
      deliveryDate: new Date(getValues('deliveryDate'))?.toISOString() ?? '',
    });

    if (res.isOk()) {
      return res.value;
    } else {
      toast({
        description: 'Error creating Quote in TMS.',
        variant: 'destructive',
      });

      return;
    }
  },

  // TODO: Leverage handleTerminatingAction()
  handleQuoteSubmissionViaURL: async ({
    email,
    getValues,
    setError,
    setLoadingDraftReply,
    finalPrice,
    profit,
    profitType,
    carrierCost,
    carrierCostType,
    maxDistance,
    isTMSQuoteSubmissionEnabled,
    isSubmitToTMS,
    setCreatedQuoteId,
    clickedSuggestion,
    setCurrentState,
    parentQuoteRequestId,
    selectedQuickQuoteId,
  }: HandleQuoteSubmissionViaURLProps) => {
    if (!email) return;

    if (!getValues('quoteNumber')) {
      setError(
        'quoteNumber',
        {
          message: 'Quote number is required',
        },
        { shouldFocus: true }
      );
      return;
    }

    if (!finalPrice || finalPrice <= 0) {
      return;
    }

    setLoadingDraftReply(true);

    const roundedFinalPrice = _.round(finalPrice);
    const expirationDateValue = getValues('quoteExpirationDate');
    const etaValue = getValues('quoteEta');

    const res = await submitQuoteViaURL(email.id, {
      quoteAmount: roundedFinalPrice,
      quoteNumber: getValues('quoteNumber') || '',
      expirationDate: expirationDateValue
        ? new Date(expirationDateValue)
        : new Date(),
      eta: etaValue ? new Date(etaValue) : new Date(),
    });

    if (res.isOk()) {
      toast({
        description: 'Successfully submitted quote via hyperlink.',
        variant: 'success',
      });
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
      setLoadingDraftReply(false);
      return; // don't proceed with TMS submission if quote submission via URL fails
    }

    if (isTMSQuoteSubmissionEnabled && isSubmitToTMS) {
      const customerId = getValues('customerName');
      if (!customerId) {
        setLoadingDraftReply(false);
        return;
      }

      const createdQuote = await useHelperFunctions.processQuoteTMSSubmission({
        customerId: customerId.toString(),
        finalPrice: finalPrice,
        getValues,
        setError,
      });

      const createdQuoteId = createdQuote?.quoteExternalId ?? '';

      setCreatedQuoteId(createdQuoteId);
      setLoadingDraftReply(false);
    }

    // Use the utility function for final carrier cost and margin calculations
    const { flatCarrierCost: finalFlatCarrierCost, finalProfit } =
      calculatePricing(
        carrierCost,
        carrierCostType,
        profit,
        profitType,
        maxDistance
      );

    // TODO: Remove marginType and use profitType instead
    await updateQuoteRequestSuggestion(
      parentQuoteRequestId,
      SuggestionStatus.Accepted,
      {
        finalQuotePrice: _.round(finalPrice ?? 0),
        finalMargin: _.round(finalProfit),
        marginType: profitType,
        finalCarrierCost: _.round(finalFlatCarrierCost),
        carrierCostType: carrierCostType,
      },
      selectedQuickQuoteId
    );

    // only remove suggestion from list at the very end of a successful quote submission
    if (clickedSuggestion) {
      setCurrentState((prevState) => ({
        ...prevState,
        clickedSuggestion: null,
        curSuggestionList: prevState.curSuggestionList.filter(
          (s) => s.id !== clickedSuggestion.id
        ),
      }));
    }

    setLoadingDraftReply(false);
  },
};

const convertFormStopstoResponseStops = (
  stops: QuoteFormStop[]
): QuickQuoteResponseStop[] =>
  stops.map((stop) => ({
    order: stop.order,
    city: stop.city,
    state: stop.state,
    zip: stop.zip,
    country: stop.country as QuoteCountries,
  }));

const quoteErrorSourceMap = {
  [Quoting.DAT]: 'DAT',
  [Quoting.Greenscreens]: 'Triumph', // GS is rebranding to Triumph
  [Quoting.GlobalTranz]: 'GlobalTranz',
  [Quoting.TruckStop]: 'TruckStop',
};
