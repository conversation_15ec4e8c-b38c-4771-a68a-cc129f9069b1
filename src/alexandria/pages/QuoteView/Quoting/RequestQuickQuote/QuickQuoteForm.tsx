import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
  Controller,
  FormProvider,
  SubmitHandler,
  useFieldArray,
  useForm,
  useFormContext,
} from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Divider } from 'antd';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit-react is in the parent dir
import JoditEditor from 'jodit-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { IJodit } from 'jodit/esm/types';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore lodash is in the parent dir
import _ from 'lodash';
import {
  ArrowRightIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  InfoIcon,
  MapIcon,
  Plus,
  XCircleIcon,
} from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore mustache is in the parent dir
import Mustache from 'mustache';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

import { AddDateDetailsButton } from 'components/AddDateDetailsButton';
import { BetaBadge } from 'components/BetaBadge';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore mustache is in the parent dir
import { Button } from 'components/Button';
import { DatePicker } from 'components/DatePicker';
import { Label } from 'components/Label';
import { LaneHistoryFromServiceChart } from 'components/LaneHistoryFromServiceChart';
import { QuoteCard, QuoteCardType } from 'components/QuoteCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Input } from 'components/input';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import CarrierPriceCalculator, {
  CarrierPriceCalculatorParent,
} from 'components/pricing/CarrierPriceCalculator';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import {
  DrumkitPlatform,
  SidebarStateContext,
  isEmailPlatform,
} from 'contexts/sidebarStateContext';
import { isCustomerSupportedTMS, useCustomers } from 'hooks/useCustomers';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import DATLogo from 'icons/DAT';
import InfoCircleIcon from 'icons/InfoCircle';
import { LaneHistoryResponse, SourceHistory } from 'lib/api/getLaneHistory';
import { LaneHistoryFromServiceResponse } from 'lib/api/getLaneHistoryFromService';
import {
  QuickQuoteResponse,
  QuickQuoteResponseStop,
  RouteSegment,
  getQuickQuote,
} from 'lib/api/getQuickQuote';
import { getUserDATInfo } from 'lib/api/getUserDATInfo';
import { updateQuoteRequestSuggestion } from 'lib/api/updateQuoteRequestSuggestion';
import { UpdateLaneRateRequest, updateRateView } from 'lib/api/updateRateView';
import { getCurrentTab, getTabHTML } from 'lib/chromeScripting/util';
import { QuotingPortal, newQuotingPortal } from 'lib/hosts/quoting/interface';
import { createMailClientInstance } from 'lib/mailclient/interface';
import {
  DATQuoteLocationType,
  DATQuoteTimeframe,
  EMPTY_STOP,
  QuoteCountries,
  SelectedQuoteType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Email } from 'types/Email';
import { Stop, TransportType } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import { SubmitQuoteToPortalMessage } from 'types/chromescript/QuotingPortal';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import {
  Quoting,
  QuotingPortals,
  TMS,
  integrationNameMap,
} from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import {
  QuoteChanges,
  SuggestedQuoteChange,
} from 'types/suggestions/QuoteSuggestions';
import captureException from 'utils/captureException';
import convertZipPlus4ToRegular from 'utils/convertExtendedZipToRegular';
import {
  copyToClipboard,
  createRichTextContent,
  isHTMLContent,
} from 'utils/copyToClipboard';
import { formatCurrency } from 'utils/formatCurrency';
import { titleCase } from 'utils/formatStrings';
import { getJoditEditorConfig, nl2br } from 'utils/getJoditEditorHelpers';
import { getNewCustomerURLInTMS } from 'utils/getNewCustomerURLInTMS';
import { getQuoteURLInTMS } from 'utils/getQuoteURLInTMS';
import { constructGoogleMapsUrl } from 'utils/googleMaps';
import { mapCustomerToAntdOptions } from 'utils/loadInfoAndBuilding';
import { calculatePricing } from 'utils/priceCalculations';
import { cn } from 'utils/shadcn';

import { DATLaneHistory } from './DATLaneHistory';
import { LaneHistoryTrends } from './LaneHistoryTrends';
import { useHelperFunctions } from './helperFunctions';
import {
  CarrierCostType,
  ProfitType,
  QuickQuoteInputs,
  QuickQuoteTextInputProps,
  QuoteFormStop,
  TransportTypeOption,
  getTransportTypeOptions,
} from './types';

export const QuickQuoteTextInput = (props: QuickQuoteTextInputProps) => {
  const {
    formState: { errors },
  } = useFormContext<QuickQuoteInputs>();

  const fieldName = props.name;

  const getStopLocationError = (fieldName: string) => {
    const match = fieldName.match(/^stops\.(\d+)\.location$/);
    if (match) {
      const stopIndex = parseInt(match[1], 10);
      return errors.stops?.[stopIndex]?.location;
    }
    return false;
  };

  const stopLocationError = getStopLocationError(fieldName);

  const inputClassName = stopLocationError
    ? `${props.inputClassName || ''} bg-error-50 border-error-500`
    : props.inputClassName;

  return <RHFTextInput {...props} inputClassName={inputClassName} />;
};

export enum FuelType {
  DOE = 'DOE',
  DAT = 'DAT',
  Portal = 'Portal', // e.g. Freightview, E2Open, etc.
  None = 'None', // Handles initialization logic
}

export type DistanceSource = Quoting | QuotingPortals;

export default function QuickQuoteForm({ email }: { email: Maybe<Email> }) {
  const { toast } = useToast();
  const posthog = usePostHog();

  const {
    serviceFeaturesEnabled: {
      isGetLaneRateFromServiceEnabled,
      isMultiStopQuickQuoteEnabled,
      isQuoteLaneHistoryEnabled,
      isQuoteSubmissionToServiceEnabled,
      isQuoteSubmissionViaURLEnabled,
      isTMSLaneHistoryEnabled,
      isTMSQuoteSubmissionEnabled,
      isOnDemandDATLaneHistoryEnabled,
    },
    configurations: { defaultPriceMargin, defaultPriceMarginType },
    tmsIntegrations,
    quotingIntegrations,
    quickQuoteConfig,
    serviceID,
  } = useServiceFeatures();

  const shouldShowLaneHistoryTrends =
    isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled;

  const {
    currentState: {
      drumkitPlatform,
      threadItemId,
      isOutlookReply,
      clickedSuggestion,
      goToSuggestionInCarousel,
      curSuggestionList,
      tabId,
      openExternalUrl,
    },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const [quoteResponse, setQuoteResponse] =
    useState<Maybe<QuickQuoteResponse>>(null);
  const [hasThirdPartyQuoteURLs, setHasThirdPartyQuoteURLs] =
    useState<boolean>(false);

  // Lane history state controls
  const [laneHistory, setLaneHistory] =
    useState<Maybe<LaneHistoryResponse>>(null);
  const [isLoadingLaneHistory, setIsLoadingLaneHistory] = useState(false);
  const [tmsLaneHistory, setTMSLaneHistory] =
    useState<Maybe<SourceHistory[]>>(null);
  const [tmsLaneHistorySelectedTierIndex, setTmsLaneHistorySelectedTierIndex] =
    useState<Maybe<number>>(null);

  const [laneHistoryFromService, setLaneHistoryFromService] =
    useState<Maybe<LaneHistoryFromServiceResponse>>(null);
  const [isLoadingLaneHistoryFromService, setIsLoadingLaneHistoryFromService] =
    useState(false);

  const [quoteNotConfident, setQuoteNotConfident] = useState<boolean>(false);
  const [selectedQuote, setSelectedQuote] = useState<SelectedQuoteType>(
    SelectedQuoteType.GS_NETWORK
  );
  const [quoteCards, setQuoteCards] = useState<QuoteCardType[]>([]);
  const [quoteCardErrors, setQuoteCardErrors] = useState<string[]>([]);
  const [hasUserSelectedCard, setHasUserSelectedCard] = useState(false);
  const [carrierCost, setCarrierCost] = useState(0);
  const [carrierCostType, setCarrierCostType] = useState<CarrierCostType>(
    CarrierCostType.Flat
  );
  const [maxDistance, setMaxDistance] = useState(0);
  const [distanceSource, setDistanceSource] =
    useState<Maybe<DistanceSource>>(null);

  const [routeDetails, setRouteDetails] =
    useState<Maybe<{ source: DistanceSource; legs: RouteSegment[] }>>(null);

  const [stopFee, setStopFee] = useState(0);
  const [intermediateStopCount, setIntermediateStopCount] = useState(0);
  const lastAppliedStopFeeRef = useRef<number>(0);

  // Carousel state for route stops
  const [currentStopIndex, setCurrentStopIndex] = useState(0);
  const [stopsExpanded, setStopsExpanded] = useState(false);
  const stopsContainerRef = useRef<HTMLDivElement>(null);

  const {
    customers,
    isLoading: customersLoading,
    tmsTenant,
    refreshCustomers: handleRefreshCustomers,
    resetCustomerSearch: handleResetCustomerSearch,
    customerSearch: handleCustomerSearch,
  } = useCustomers(tmsIntegrations, {
    toast,
    suggestedCustomerId: (clickedSuggestion?.suggested as QuoteChanges)
      ?.customerExternalTMSID,
  });

  const [profitType, setProfitType] = useState<ProfitType>(
    defaultPriceMarginType
      ? defaultPriceMarginType
      : quickQuoteConfig?.defaultMarginType
        ? (quickQuoteConfig?.defaultMarginType as ProfitType)
        : ProfitType.Percentage
  );

  const [profit, setProfit] = useState(
    defaultPriceMargin
      ? defaultPriceMargin
      : profitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10
  );

  const [finalPrice, setFinalPrice] = useState<Maybe<number>>(null);

  // If carrier cost is per mile, finalFlatPrice is calculated as (cost + margin) * distance
  const [finalFlatPrice, setFinalFlatPrice] = useState(finalPrice);
  const [finalPriceFormat, setFinalPriceFormat] = useState<
    CarrierCostType | 'Both'
  >('Both');

  // Final flat price minus fuel surcharge
  const [fuelEstimate, setFuelEstimate] = useState(0);
  const [datFuelSurcharge, setDATFuelSurcharge] = useState<Maybe<number>>(null);

  const [draftResponse, setDraftResponse] = useState('');
  const [userEditedDraft, setUserEditedDraft] = useState(false);
  const [hasCopiedDraftResponse, setHasCopiedDraftResponse] = useState(false);
  // If user edits message but wants to toggle the format of the price,
  // we need to keep track of the current price string to replace it with the new one
  const [currentPriceString, setCurrentPriceString] = useState('');

  const [loadingDraftReply, setLoadingDraftReply] = useState(false);
  const [isSubmitToTMS, setIsSubmitToTMS] = useState<boolean>(true);
  const [createdQuoteId, setCreatedQuoteId] = useState<Maybe<string>>(null);

  // This is the ID of the parent quote request that we reference after calling GetQuickQuote. It is used to
  // update the quote request to accepted at the end of the quick quote flow.
  const [parentQuoteRequestId, setParentQuoteRequestId] = useState<number>(0);

  const [greenscreensQuoteID, setGreenscreensQuoteID] = useState<string>('');

  const [serviceUsesDAT, setServiceUsesDAT] = useState<boolean>(false);
  const [isLoadingDAT, setIsLoadingDAT] = useState<boolean>(false);
  const [datEmailAddress, setDATEmailAddress] = useState<string>('');
  const [hasGrantedDATPermissions, setHasGrantedDATPermissions] =
    useState<Maybe<boolean>>(null);

  // For Quote Submission in TMS
  const [tmsName, setTMSName] = useState<Maybe<TMS>>(null);

  useEffect(() => {
    // TODO: Support quote submission for multiple TMSes
    if (tmsIntegrations && tmsIntegrations.length > 0) {
      setTMSName(tmsIntegrations[0].name as TMS);
    }
  }, [tmsIntegrations]);

  const newCustomerURLInTMS = useMemo(
    () => getNewCustomerURLInTMS(tmsName, tmsTenant),
    [tmsName, tmsTenant]
  );

  const quoteURLInTMS = useMemo(() => {
    if (!createdQuoteId) {
      return null;
    }

    return getQuoteURLInTMS(createdQuoteId, tmsName, tmsTenant);
  }, [createdQuoteId, tmsName, tmsTenant]);

  // For legacy 2-stop form
  const [hasAddedQuoteDates, setHasAddedQuoteDates] = useState<boolean>(false);
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [submitQuoteLoading, setSubmitQuoteLoading] = useState<boolean>(false);
  const [isSubmittingToPortal, setIsSubmittingToPortal] = useState(false);

  const [portal, setPortal] = useState<Undef<QuotingPortal>>(undefined);
  const [showSubmitToPortalButtons, setShowSubmitToPortalButtons] =
    useState<boolean>(false);

  const hasDATRateView = quoteCards.some(
    (card) => card.type === SelectedQuoteType.DAT_RATEVIEW
  );

  const handleUpdateDATRate = async (
    timeframe: DATQuoteTimeframe,
    areaType: DATQuoteLocationType,
    setUpdateRateViewError: (error: Maybe<string>) => void
  ) => {
    setIsLoadingDAT(true);
    const formValues = getValues();

    if (quoteResponse == undefined || quoteResponse?.stops.length < 2) {
      toast({
        description: 'Insufficient stops for update',
        variant: 'destructive',
      });

      setIsLoadingDAT(false);
      return;
    }

    const origin = quoteResponse?.stops[0];
    const destination = quoteResponse?.stops[quoteResponse.stops.length - 1];
    const transportType = formValues.transportType;

    const request: UpdateLaneRateRequest = {
      originCity: origin.city,
      originState: origin.state,
      originZip: origin.zip,
      originCountry: QuoteCountries.USA,
      destinationCity: destination.city,
      destinationState: destination.state,
      destinationZip: destination.zip,
      destinationCountry: QuoteCountries.USA,
      transportType,
      specificTimeFrame: timeframe,
      specificAreaType: areaType,
    };

    const res = await updateRateView(request);

    if (!res.isOk()) {
      toast({
        description: 'Failed to update RateView',
        variant: 'destructive',
      });

      setUpdateRateViewError(res.error.message);

      setIsLoadingDAT(false);
      return;
    }

    const data = res.value;
    toast({
      description: 'RateView updated successfully',
      variant: 'success',
    });

    setQuoteCards((prev) =>
      prev.map((card) => {
        if (card.type !== SelectedQuoteType.DAT_RATEVIEW) {
          return card;
        }

        return {
          ...card,
          cost: data.rate.target,
          priceRange: {
            lowEstimate: data.rate.low,
            highEstimate: data.rate.high,
          },
          tooltipContent: card.tooltipContent
            ? {
                ...card.tooltipContent,
                companies: data.metadata?.companies,
                destinationType: areaType,
                originType: areaType,
                reports: data.metadata?.reports,
                timeframe: data.metadata?.timeframe,
              }
            : undefined,
        } as QuoteCardType;
      })
    );

    setDATFuelSurcharge(Number(data.rate.fuelSurchargePerMile));
    setIsLoadingDAT(false);
  };

  const joditRef = useRef<Maybe<IJodit>>(null);

  const transportTypeOptions = useMemo(
    () => getTransportTypeOptions(quickQuoteConfig),
    [quickQuoteConfig]
  );

  // Properties for GetQuickQuote button Posthog logging
  const getQuickQuoteProperties = useMemo(() => {
    return {
      serviceID,
      hasQRSuggestions: curSuggestionList.some(
        (s) => s.pipeline === SuggestionPipelines.QuickQuote
      ),
      profitType,
      carrierCostType,
      suggestionId: clickedSuggestion?.id,
    };
  }, [
    serviceID,
    curSuggestionList,
    profitType,
    carrierCostType,
    clickedSuggestion,
  ]);

  // Properties for CopyQuoteToClipboard button Posthog logging
  const copyToClipboardProperties = useMemo(() => {
    if (!quoteResponse && !quoteNotConfident) return {};

    const { flatCarrierCost, finalProfit } = calculatePricing(
      carrierCost,
      carrierCostType,
      profit,
      profitType,
      maxDistance
    );

    return {
      serviceID,
      parentQuoteRequestId,
      finalQuotePrice: finalFlatPrice ?? 0,
      finalMargin: finalProfit,
      marginType: profitType,
      finalCarrierCost: flatCarrierCost,
      carrierCostType: carrierCostType,
      hasQRSuggestions: curSuggestionList.some(
        (s) => s.pipeline === SuggestionPipelines.QuickQuote
      ),
      suggestionId: clickedSuggestion?.id,
    };
  }, [
    quoteResponse,
    quoteNotConfident,
    carrierCost,
    carrierCostType,
    profit,
    profitType,
    maxDistance,
    serviceID,
    parentQuoteRequestId,
    finalFlatPrice,
    curSuggestionList,
    clickedSuggestion,
  ]);

  useEffect(() => {
    async function checkTab() {
      const tab = await getCurrentTab();
      if (!tab?.id) return;

      const portal = newQuotingPortal(tab?.url);

      setPortal(portal);
      setShowSubmitToPortalButtons(!!portal);
    }
    checkTab();
  }, []);

  const fetchUserDATInfo = async () => {
    const res = await getUserDATInfo();
    if (res.isOk()) {
      setHasGrantedDATPermissions(res.value.hasGrantedDATPermissions);
    }
  };

  useEffect(() => {
    const serviceHasDATIntegration = quotingIntegrations.some(
      (qIntegration) => qIntegration.name === Quoting.DAT
    );

    if (serviceHasDATIntegration) {
      setServiceUsesDAT(true);
    }
  }, [quotingIntegrations]);

  useEffect(() => {
    if (serviceUsesDAT) {
      fetchUserDATInfo();
    }
  }, [serviceUsesDAT]);

  const memoizedDefaultValues = useMemo<QuickQuoteInputs>(() => {
    const suggestedFields = clickedSuggestion?.suggested as QuoteChanges;
    // Somewhere along the way the array of stops returned by API
    // is getting converted to an object with numeric keys (likely by React state serialization)
    // so we enforce it as an array so AI-filling works
    const suggestedStops = Array.isArray(suggestedFields?.stops)
      ? suggestedFields.stops
      : suggestedFields?.stops
        ? (Object.values(suggestedFields.stops) as Stop[])
        : [];
    const hasMultipleStops = suggestedStops.length > 2;

    const firstStop = suggestedStops[0];
    const hasValidReadyTime =
      hasMultipleStops &&
      firstStop?.stopType === 'pickup' &&
      firstStop?.readyTime &&
      firstStop.readyTime !== undefined;

    const pickupDate: Maybe<Date> = hasValidReadyTime
      ? new Date(firstStop.readyTime!)
      : suggestedFields?.pickupDate
        ? new Date(suggestedFields.pickupDate)
        : null;

    const hasValidMustDeliver =
      hasMultipleStops &&
      firstStop?.stopType === 'dropoff' &&
      firstStop?.mustDeliver &&
      firstStop.mustDeliver !== undefined;

    const deliveryDate: Maybe<Date> = hasValidMustDeliver
      ? new Date(firstStop.mustDeliver!)
      : suggestedFields?.deliveryDate
        ? new Date(suggestedFields.deliveryDate)
        : null;

    let stops: QuoteFormStop[] = [];

    if (hasMultipleStops && isMultiStopQuickQuoteEnabled) {
      stops = suggestedStops.map((stop, index) => ({
        order: index,
        location:
          stop.address.zip || `${stop.address.city}, ${stop.address.state}`,
        city: stop.address.city,
        state: stop.address.state,
        zip: stop.address.zip,
        country: stop.address.country as QuoteCountries,
      }));
    } else if (suggestedFields) {
      // Process suggested pickup location
      const pickupLocation = useHelperFunctions.parseLocation(
        suggestedFields.pickupZip
          ? suggestedFields.pickupZip
          : `${suggestedFields.pickupCity}, ${suggestedFields.pickupState}`
      );

      const deliveryLocation = useHelperFunctions.parseLocation(
        suggestedFields?.deliveryZip
          ? suggestedFields.deliveryZip
          : `${suggestedFields?.deliveryCity}, ${suggestedFields?.deliveryState}`
      );

      stops = [
        {
          order: 0,
          location: pickupLocation
            ? pickupLocation.zip
              ? pickupLocation.zip
              : `${pickupLocation.city}, ${pickupLocation.state}`
            : '',
          city: pickupLocation?.city ?? '',
          state: pickupLocation?.state ?? '',
          zip: pickupLocation?.zip ?? '',
          country:
            (pickupLocation?.country as QuoteCountries) ?? QuoteCountries.USA,
        },
        {
          order: 1,
          location: deliveryLocation
            ? deliveryLocation.zip
              ? deliveryLocation.zip
              : `${deliveryLocation.city}, ${deliveryLocation.state}`
            : '',
          city: deliveryLocation?.city ?? '',
          state: deliveryLocation?.state ?? '',
          zip: deliveryLocation?.zip ?? '',
          country:
            (deliveryLocation?.country as QuoteCountries) ?? QuoteCountries.USA,
        },
      ];
    } else {
      // Else initialize empty stops
      stops = [EMPTY_STOP, EMPTY_STOP];
    }

    // Determine transport type with fallback to default
    let transportType = suggestedFields?.transportType;
    if (!transportType) {
      if (quickQuoteConfig && quickQuoteConfig.defaultTransportType) {
        transportType =
          quickQuoteConfig.defaultTransportType.toUpperCase() as TransportType;
      } else {
        transportType = TransportType.VAN;
      }
    }

    const parsedValues = {
      customerName: suggestedFields?.customerExternalTMSID ?? '',
      transportType: transportType,
      pickupDate: pickupDate,
      deliveryDate: deliveryDate,
      stops: stops,
      isSubmitToTMS: false,
    };

    return parsedValues as QuickQuoteInputs;
  }, [clickedSuggestion, quickQuoteConfig]);

  const formMethods = useForm<QuickQuoteInputs>({
    defaultValues: memoizedDefaultValues,
    mode: 'onSubmit',
  });

  const {
    control,
    handleSubmit,
    reset,
    getValues,
    setValue,
    setError,
    formState: { errors, isSubmitting },
  } = formMethods;

  const { fields, append, remove } = useFieldArray<QuickQuoteInputs, 'stops'>({
    control,
    name: 'stops',
  });

  const handleAddStop = () => {
    // Get current dropoff data before removing it
    const currentDropoff = getValues(`stops.${fields.length - 1}`);

    // Remove the dropoff stop
    remove(fields.length - 1);

    // Add the new intermediate stop
    append({
      order: fields.length,
      city: '',
      state: '',
      zip: '',
      country: QuoteCountries.USA,
      location: '',
    } as QuoteFormStop);

    // Add back the dropoff stop with preserved data
    append({
      order: fields.length,
      city: currentDropoff.city || '',
      state: currentDropoff.state || '',
      zip: currentDropoff.zip || '',
      country: currentDropoff.country || QuoteCountries.USA,
      location: currentDropoff.location || '',
    } as QuoteFormStop);

    // Focus on the new stop's location input
    setTimeout(() => {
      const newStopIndex = fields.length - 1; // The new stop is second to last
      const locationInput = document.querySelector(
        `input[name="stops.${newStopIndex}.location"]`
      ) as HTMLInputElement;
      locationInput?.focus();
    }, 100);
  };

  function formatDraftTemplate(
    templateBody: string,
    transportType: TransportType,
    stops: QuoteFormStop[] | QuickQuoteResponseStop[],
    pickupDate: Date | undefined,
    deliveryDate: Date | undefined,
    rate: string
  ): string {
    const pickupCity = stops[0].city;
    const pickupState = stops[0].state;
    const deliveryCity = stops[stops.length - 1].city;
    const deliveryState = stops[stops.length - 1].state;

    const pickupLocation = `${useHelperFunctions.toTitleCase(pickupCity || '')}, ${pickupState || ''}`;
    const deliveryLocation = `${useHelperFunctions.toTitleCase(deliveryCity || '')}, ${deliveryState || ''}`;

    if (pickupDate && isNaN(pickupDate.getTime())) {
      pickupDate = undefined;
    }

    if (deliveryDate && isNaN(deliveryDate.getTime())) {
      deliveryDate = undefined;
    }

    return Mustache.render(templateBody, {
      // Mustache template variables must match exact variable names in the template defined in BE,
      // common/models/email_template.go
      pickupLocation,
      deliveryLocation,
      transportType: transportType.toLowerCase(),
      pickupDate: pickupDate?.toDateString(),
      deliveryDate: deliveryDate?.toDateString(),
      rate: rate,
      isMultiStop: stops.length > 2,
      stops: stops,
    });
  }

  const formatFinalPriceInDraft = (
    flatPrice: Maybe<number>,
    format: CarrierCostType | 'Both',
    fuelEstimate?: number
  ): string => {
    const price = flatPrice ?? 0;
    if (format === CarrierCostType.Linehaul && fuelEstimate) {
      return `${formatCurrency(price - fuelEstimate, 'USD')}`;
    }

    if (!maxDistance) return formatCurrency(price, 'USD');

    if (format === 'Both') {
      return `${formatCurrency(price, 'USD')} (${formatCurrency(
        price / maxDistance,
        'USD',
        2
      )}/mile)`;
    } else if (format === CarrierCostType.PerMile) {
      return `${formatCurrency(price / maxDistance, 'USD', 2)}/mile`;
    } else {
      return formatCurrency(price, 'USD');
    }
  };

  // Setting default quote card
  useEffect(() => {
    if (!quoteCards?.length || hasUserSelectedCard) {
      return;
    }

    // If TMS lane history is enabled, always select that box
    if (
      isTMSLaneHistoryEnabled &&
      laneHistory &&
      Object.values(laneHistory?.resultsBySource)?.length > 0
    ) {
      const laneHistoryCard = quoteCards.find(
        (c) =>
          c.type === SelectedQuoteType.LANE_HISTORY ||
          c.type === SelectedQuoteType.MCLEOD_LANE_HISTORY ||
          c.type === SelectedQuoteType.TURVO_LANE_HISTORY ||
          c.type === SelectedQuoteType.GLOBALTRANZ_LANE_HISTORY ||
          c.type === SelectedQuoteType.GLOBALTRANZ_TMS_LANE_HISTORY
      );
      if (laneHistoryCard) {
        setSelectedQuote(laneHistoryCard.type);
        setCarrierCost(_.round(laneHistoryCard.cost, 2));
        return;
      }
    }

    const hasDATCarrier = quoteCards.find(
      (c) => c.type === SelectedQuoteType.DAT_RATEVIEW
    );
    const defaultCarrier =
      hasDATCarrier ??
      quoteCards.reduce((prev, curr) => (prev.cost < curr.cost ? prev : curr));

    setSelectedQuote(defaultCarrier.type);
    setCarrierCost(_.round(defaultCarrier.cost));
  }, [quoteCards, isTMSLaneHistoryEnabled, laneHistory, hasUserSelectedCard]);

  const handleEnableDATAccess = async () => {
    setIsLoadingDAT(true);

    await useHelperFunctions.enableDATIndividualAccess({
      datEmailAddress,
      setHasGrantedDATPermissions,
    });

    setIsLoadingDAT(false);
  };

  const getSelectedQuickQuoteId = (): number | undefined => {
    if (!quoteResponse || !quoteResponse.quotes || !selectedQuote) {
      return undefined;
    }

    // Type for elements in quoteResponse.quotes (respQuotes) has source: Quoting
    // but we might encounter 'tms_lane_history' as a string if not in Quoting enum.
    let selectedQuoteMatcher: (q: {
      id: number;
      source: string;
      [key: string]: any;
    }) => boolean;

    switch (selectedQuote) {
      case SelectedQuoteType.DAT_RATEVIEW:
        selectedQuoteMatcher = (q) => q.source === Quoting.DAT;
        break;
      case SelectedQuoteType.MCLEOD_LANE_HISTORY:
      case SelectedQuoteType.TURVO_LANE_HISTORY:
      case SelectedQuoteType.GLOBALTRANZ_LANE_HISTORY:
      case SelectedQuoteType.GLOBALTRANZ_TMS_LANE_HISTORY:
      case SelectedQuoteType.LANE_HISTORY:
        // Compare source as string to handle 'tms_lane_history' if it's not in Quoting enum
        selectedQuoteMatcher = (q) =>
          (q.source as string) === 'tms_lane_history';
        break;
      case SelectedQuoteType.GS_NETWORK: // Typically Greenscreens Network
      case SelectedQuoteType.GS_BUYPOWER: // Typically Greenscreens BuyPower
        selectedQuoteMatcher = (q) => q.source === Quoting.Greenscreens;
        break;
      case SelectedQuoteType.TRUCKSTOP_POSTED:
      case SelectedQuoteType.TRUCKSTOP_BOOKED:
        selectedQuoteMatcher = (q) => q.source === Quoting.TruckStop;
        break;
      default:
        // If selectedQuote doesn't map to a known quick quote source we track.
        return undefined;
    }
    const foundQuote = quoteResponse.quotes.find(selectedQuoteMatcher);
    return foundQuote?.id;
  };

  const [DATLaneHistoryData, setDATLaneHistoryData] =
    useState<Maybe<DATLaneHistory>>(null);
  const [hasPulledDATLaneHistory, setHasPulledDATLaneHistory] =
    useState<boolean>(false);

  const onSubmitForm: SubmitHandler<QuickQuoteInputs> = async (formValues) => {
    // Reset state variables for new quote
    setCarrierCostType(CarrierCostType.Flat);
    setLaneHistory(null);
    setLaneHistoryFromService(null);
    setDraftResponse('');
    setUserEditedDraft(false);
    setFinalPrice(null);
    setFinalPriceFormat(CarrierCostType.Flat);
    setDATLaneHistoryData(null);
    setHasPulledDATLaneHistory(false);
    setHasUserSelectedCard(false);

    // Check for ZIP+4 inputs and convert to regular ZIP if necessary
    convertZipPlus4ToRegular({ formValues, setValue });

    const currentSelectedQuickQuoteId = getSelectedQuickQuoteId();

    try {
      await useHelperFunctions.onSubmitForm({
        isMultiStopQuickQuoteEnabled,
        getValues,
        formValues,
        setIsSubmitToTMS,
        setCreatedQuoteId,
        setQuoteResponse,
        setQuoteCards,
        setQuoteCardErrors,
        isQuoteSubmissionViaURLEnabled,
        email,
        setHasThirdPartyQuoteURLs,
        setValue,
        isGetLaneRateFromServiceEnabled,
        clickedSuggestion,
        formMethods,
        setQuoteNotConfident,
        getQuickQuote,
        isQuoteLaneHistoryEnabled,
        isTMSLaneHistoryEnabled,
        setIsLoadingLaneHistory,
        setLaneHistory,
        setCarrierCost,
        setProfit,
        profitType,
        setError,
        setParentQuoteRequestId,
        setGreenscreensQuoteID,
        isQuoteSubmissionToServiceEnabled,
        setDATFuelSurcharge,
        selectedQuickQuoteId: currentSelectedQuickQuoteId,
        serviceID,
        posthog,
      });
    } catch (error) {
      captureException(error, {
        functionName: 'onSubmitForm',
      });
    }

    // Fetch lane history from service if enabled - will be called after quote is available
    if (isGetLaneRateFromServiceEnabled) {
      setIsLoadingLaneHistoryFromService(true);
    }
  };

  useEffect(() => {
    // If TMS lane history is enabled, set the selected lane tier to the first tier in the list
    const tmsHistoriesKey = Object.keys(
      laneHistory?.resultsBySource || {}
    ).find((source) => Object.values(TMS).includes(source as TMS));

    if (tmsHistoriesKey) {
      setTMSLaneHistory(
        laneHistory?.resultsBySource[tmsHistoriesKey as TMS] || null
      );
      setTmsLaneHistorySelectedTierIndex(0);
    }
  }, [laneHistory]);

  // Fetch lane history from service when quote becomes available
  // TODO: Multi-stop for Trident ENG-3993
  useEffect(() => {
    if (
      isGetLaneRateFromServiceEnabled &&
      quoteResponse &&
      isLoadingLaneHistoryFromService
    ) {
      const fetchLaneHistoryData = async () => {
        const lastIndex = quoteResponse.stops.length - 1;
        try {
          const laneHistoryRes =
            await useHelperFunctions.fetchLaneHistoryFromService({
              quoteRequestId: quoteResponse.quoteRequestId,
              originCity: quoteResponse.stops[0].city,
              originState: quoteResponse.stops[0].state,
              originZip: quoteResponse.stops[0].zip,
              originCountry: quoteResponse.stops[0].country ?? '',
              destinationCity: quoteResponse.stops[lastIndex].city,
              destinationState: quoteResponse.stops[lastIndex].state,
              destinationZip: quoteResponse.stops[lastIndex].zip,
              destinationCountry: quoteResponse.stops[lastIndex].country ?? '',
              transportType: getValues('transportType'),
            });
          if (laneHistoryRes.isOk()) {
            setLaneHistoryFromService(laneHistoryRes.value);
          }
        } catch (error) {
          captureException(error, {
            functionName: 'fetchLaneHistoryFromService',
          });
        } finally {
          setIsLoadingLaneHistoryFromService(false);
        }
      };

      fetchLaneHistoryData();
    }
  }, [
    isGetLaneRateFromServiceEnabled,
    quoteResponse,
    isLoadingLaneHistoryFromService,
  ]);

  const toggleLaneHistoryGraph = (option: string) => {
    if (!tmsLaneHistory) return;

    const tierIndex = tmsLaneHistory?.findIndex(
      (history) => history.laneTier === option
    );
    if (tierIndex === -1) return;

    setTmsLaneHistorySelectedTierIndex(tierIndex);
  };

  // If no suggestions have been applied and there are QQ ones, apply the first one
  useEffect(() => {
    const firstQuickQuoteSuggestion = curSuggestionList.find(
      (s) => s.pipeline === SuggestionPipelines.QuickQuote
    );

    // Update clickedSuggestion whenever curSuggestionList changes and there's a QuickQuote suggestion
    if (
      firstQuickQuoteSuggestion &&
      clickedSuggestion !== firstQuickQuoteSuggestion
    ) {
      setCurrentState((prevState) => ({
        ...prevState,
        clickedSuggestion: firstQuickQuoteSuggestion,
      }));
      goToSuggestionInCarousel({
        suggestionID: firstQuickQuoteSuggestion?.id,
      });
    }
  }, [curSuggestionList, goToSuggestionInCarousel]);

  const handleCopyToClipboard = async () => {
    const success = await copyToClipboard(
      isHTMLContent(draftResponse)
        ? createRichTextContent(draftResponse)
        : draftResponse,
      { showToast: false } // Don't show toast since we already show tooltip
    );

    if (success) {
      setHasCopiedDraftResponse(true);
      handleTerminatingAction();
      // Reset copied state after a delay
      setTimeout(() => setHasCopiedDraftResponse(false), 2000);
    }
  };

  // Update draft response when quote, finalPrice or finalPriceFormat changes.
  // If user edited the draft, then replace just the price substring, not all of their changes.
  useEffect(() => {
    const pickupDate = new Date(getValues('pickupDate'));
    const deliveryDate = new Date(getValues('deliveryDate'));
    const stops: QuoteFormStop[] | QuickQuoteResponseStop[] =
      quoteResponse?.stops ?? getValues('stops');

    const finalPrice = formatFinalPriceInDraft(
      finalFlatPrice,
      finalPriceFormat,
      fuelEstimate
    );

    // Get the template body
    const templateBody = quoteResponse?.quoteReplyDraftTemplate?.body || '';

    // Get template draft
    const templateDraft = formatDraftTemplate(
      templateBody,
      getValues('transportType'),
      stops,
      pickupDate,
      deliveryDate,
      finalPrice
    );

    // Assume the template is the final draft
    let finalDraft = templateDraft;

    // If draft exists and user didn't edit (or did edit but kept the price), only update price
    const onlyUpdateDraftPrice =
      draftResponse &&
      (!userEditedDraft || draftResponse.includes(currentPriceString));

    if (onlyUpdateDraftPrice) {
      finalDraft = draftResponse.replace(currentPriceString, finalPrice);
    }

    setDraftResponse(nl2br(finalDraft));
    setCurrentPriceString(finalPrice);
  }, [quoteResponse, finalFlatPrice, finalPriceFormat]);

  // useEffect(() => {
  //   if (quoteResponse) {
  //     setFinalFlatPrice(
  //       quoteResponse.quotes.find(
  //         (quote) => quote.id === (selectedQuote || quoteResponse.quotes[0].id)
  //       )?.finalPrice || 0
  //     );
  //   }
  // }, [quoteResponse]);

  // Update form values when suggestion changes
  useEffect(() => {
    if (memoizedDefaultValues) {
      reset(memoizedDefaultValues);
    }
  }, [memoizedDefaultValues]);

  // Fetch suggested customer from backend if it's not in the dropdown
  useEffect(() => {
    const suggestedFields = clickedSuggestion?.suggested as QuoteChanges;
    const suggestedCustomerId = suggestedFields?.customerExternalTMSID;

    // Only run this effect when the suggestion changes, not when customers list changes
    if (suggestedCustomerId && tmsIntegrations?.[0]?.id) {
      // Check if customer is already in the list
      const existingCustomer = customers?.find(
        (c) => c.externalTMSID === suggestedCustomerId
      );

      if (!existingCustomer) {
        // Search for the customer to get full details and add to dropdown
        const fetchSuggestedCustomer = async () => {
          try {
            await handleCustomerSearch('name', suggestedCustomerId);
          } catch (error) {
            captureException(error, {
              functionName: 'fetchSuggestedCustomer',
            });
          }
        };

        fetchSuggestedCustomer();
      }
    }
  }, [clickedSuggestion?.id, tmsIntegrations]); // Only depend on suggestion ID, not the whole customers array

  useEffect(() => {
    const isQuoteResponseValid = quoteResponse;
    const isQuoteResponseEmpty = quoteResponse?.quotes?.length === 0;

    const isQQSuggestionWithDistance =
      clickedSuggestion?.pipeline === SuggestionPipelines.QuickQuote &&
      clickedSuggestion?.suggested?.distanceMiles;
    const isMultiStopQuote =
      quoteResponse?.stops?.length && quoteResponse.stops.length > 2;

    if (!isQuoteResponseValid && !quoteNotConfident) {
      return;
    }

    // First use parsed distance from suggestion/bidding portal if available
    if (isQQSuggestionWithDistance) {
      setMaxDistance(clickedSuggestion.suggested.distanceMiles ?? 0);
      setDistanceSource(clickedSuggestion.source);
    } else if (isMultiStopQuote) {
      // Otherwise, use the total distance from DAT if available.
      // (Greenscreens natively supports multi-stop but does not return the per-leg distance,
      // so we prefer total + leg distances from DAT)
      const datMultiStopQuote = quoteResponse.quotes?.find(
        (q) => q.source === Quoting.DAT
      );

      if (datMultiStopQuote) {
        setMaxDistance(datMultiStopQuote.distance);
        setDistanceSource(datMultiStopQuote.source as DistanceSource);
      }
    } else if (isQuoteResponseValid && !isQuoteResponseEmpty) {
      const maxDistanceQuote = quoteResponse.quotes?.reduce((max, current) =>
        current.distance > max.distance ? current : max
      );

      setMaxDistance(maxDistanceQuote.distance);
      setDistanceSource(maxDistanceQuote.source as DistanceSource);
    }

    // Set route details
    const quoteWithLegs =
      quoteResponse?.quotes.find(
        (q) => q.metadata?.legs && q.source === distanceSource
      ) || quoteResponse?.quotes.find((q) => q.metadata?.legs);

    if (quoteWithLegs?.metadata?.legs) {
      setRouteDetails({
        source: quoteWithLegs.source as DistanceSource,
        legs: quoteWithLegs.metadata.legs,
      });
    }

    const quoteWithStopFee = quoteResponse?.quotes.find(
      (q) => q.metadata?.stopFeeUSD
    );
    if (quoteWithStopFee?.metadata?.stopFeeUSD) {
      const fee = quoteWithStopFee.metadata.stopFeeUSD;
      setStopFee(fee);
      lastAppliedStopFeeRef.current = fee;
    }

    const count = Math.max((quoteResponse?.stops?.length || 0) - 2, 0);
    setIntermediateStopCount(count);
  }, [quoteResponse, quoteNotConfident, clickedSuggestion]);

  useEffect(() => {
    if (carrierCostType === CarrierCostType.Flat) {
      setFinalFlatPrice(finalPrice);
    } else {
      setFinalFlatPrice((finalPrice ?? 0) * maxDistance);
    }
  }, [finalPrice, carrierCostType, maxDistance]);

  useEffect(() => {
    if (
      selectedQuote !== SelectedQuoteType.DAT_LONGEST_LEG ||
      intermediateStopCount <= 0
    ) {
      return;
    }

    const prevFee = lastAppliedStopFeeRef.current;
    const diff = stopFee - prevFee;
    const deltaTrip = diff * intermediateStopCount;
    const deltaPerMile = maxDistance > 0 ? deltaTrip / maxDistance : 0;

    setQuoteCards((prev) =>
      prev.map((card): QuoteCardType => {
        if (card.type !== SelectedQuoteType.DAT_LONGEST_LEG) {
          return card;
        }

        const nextCost = (card.cost ?? 0) + deltaTrip;
        const nextCostPerMile =
          card.costPerMile != null && maxDistance > 0
            ? card.costPerMile + deltaPerMile
            : card.costPerMile;

        const nextPriceRange = card.priceRange
          ? {
              lowEstimate: card.priceRange.lowEstimate + deltaTrip,
              highEstimate: card.priceRange.highEstimate + deltaTrip,
            }
          : card.priceRange;

        const nextPriceRangePerMile =
          card.priceRangePerMile && maxDistance > 0
            ? {
                lowEstimate: card.priceRangePerMile.lowEstimate + deltaPerMile,
                highEstimate:
                  card.priceRangePerMile.highEstimate + deltaPerMile,
              }
            : card.priceRangePerMile;

        if (card.tooltipConstructor) {
          return {
            ...card,
            cost: nextCost,
            costPerMile: nextCostPerMile,
            priceRange: nextPriceRange,
            priceRangePerMile: nextPriceRangePerMile,
            tooltipContent: card.tooltipContent
              ? { ...card.tooltipContent, stopFeeUSDMedium: stopFee }
              : card.tooltipContent,
          };
        }

        return {
          ...card,
          cost: nextCost,
          costPerMile: nextCostPerMile,
          priceRange: nextPriceRange,
          priceRangePerMile: nextPriceRangePerMile,
        };
      })
    );
    lastAppliedStopFeeRef.current = stopFee;
  }, [selectedQuote, stopFee, intermediateStopCount, maxDistance]);

  useEffect(() => {
    if (selectedQuote !== SelectedQuoteType.DAT_LONGEST_LEG) {
      return;
    }

    const llCard = quoteCards.find(
      (c) => c.type === SelectedQuoteType.DAT_LONGEST_LEG
    );

    if (!llCard) {
      return;
    }

    let newBuy = 0;
    if (carrierCostType === CarrierCostType.Flat) {
      newBuy = llCard.cost ?? 0;
    } else {
      newBuy =
        llCard.costPerMile != null
          ? llCard.costPerMile
          : maxDistance > 0
            ? (llCard.cost ?? 0) / maxDistance
            : 0;
    }

    if (newBuy !== carrierCost) {
      setCarrierCost(newBuy);
    }
  }, [selectedQuote, quoteCards, carrierCostType, maxDistance, carrierCost]);

  // Keep the DAT Longest Leg tooltip in sync with the current stop fee
  useEffect(() => {
    setQuoteCards((prev) =>
      prev.map((card) => {
        if (
          card.type === SelectedQuoteType.DAT_LONGEST_LEG &&
          card.tooltipContent
        ) {
          return {
            ...card,
            tooltipContent: {
              ...card.tooltipContent,
              stopFeeUSDMedium: stopFee,
            },
          };
        }
        return card;
      })
    );
  }, [stopFee]);

  /* handleTerminatingAction should be called whenever user takes an action that "terminates" the quote request,
   * i.e. updates the QR status from "inFlight" to 1 of 2 terminating statuses: "accepted" or "rejected".
   * As of 6/4/2025, this occurs when user clicks "Submit to Portal", "Submit to TMS," "Create Draft Reply",
   * or one of the copy buttons.
   * This function
   * 1) Updates BE quote request with final quote and margin values (critical for metrics)
   * 2) Sends user quote to service (if enabled)
   * 3) Updates UI to show success or failure toast
   * TODO: Capture line haul in BE
   */
  const handleTerminatingAction = async () => {
    // Use the utility function for final price and margin calculations
    const { flatCarrierCost: finalFlatCarrierCost, finalProfit } =
      calculatePricing(
        carrierCost,
        carrierCostType,
        profit,
        profitType,
        maxDistance
      );

    if (isQuoteSubmissionToServiceEnabled) {
      useHelperFunctions.sendUserQuote({
        email,
        quoteResponse,
        parentQuoteRequestId,
        greenscreensQuoteID,
        carrierCost: _.round(finalFlatCarrierCost),
        profit: _.round(finalProfit),
        finalPrice: finalFlatPrice ?? 0,
        draftResponse,
        profitType,
      });
    }

    // TODO: Remove marginType and use profitType instead
    const currentSelectedQuickQuoteId = getSelectedQuickQuoteId();
    const result = await updateQuoteRequestSuggestion(
      parentQuoteRequestId,
      SuggestionStatus.Accepted,
      {
        finalQuotePrice: _.round(finalFlatPrice ?? 0),
        finalMargin: _.round(finalProfit),
        marginType: profitType,
        finalCarrierCost: _.round(finalFlatCarrierCost),
        carrierCostType: carrierCostType,
        customerExternalTMSId: getValues('customerName'),
      },
      currentSelectedQuickQuoteId
    );

    if (result.isErr()) {
      captureException(result.error, {
        functionName: 'handleTerminatingAction',
      });
    }
  };

  const handleSubmitQuoteToTMS = async () => {
    if (isTMSQuoteSubmissionEnabled && isSubmitToTMS) {
      const customerId = getValues('customerName');

      if (!customerId) {
        toast({
          description: 'Please enter a customer name.',
          variant: 'destructive',
        });
        return;
      }

      setSubmitQuoteLoading(true);

      const createdQuote = await useHelperFunctions.processQuoteTMSSubmission({
        customerId: customerId,
        finalPrice: finalFlatPrice,
        getValues,
        setError,
      });

      const createdQuoteId = createdQuote?.quoteExternalId ?? '';

      setCreatedQuoteId(createdQuoteId);
      setSubmitQuoteLoading(false);
    }
  };

  let handleDraftResponse: () => void;
  if (drumkitPlatform === DrumkitPlatform.Outlook) {
    // TODO: Warn user to configure signature in Portal if none found
    const mailClient = createMailClientInstance(drumkitPlatform);
    handleDraftResponse = async () => {
      if (!finalFlatPrice || finalFlatPrice <= 0) {
        return;
      }

      setLoadingDraftReply(true);

      try {
        // Convert newlines to HTML <br> tags since the mail client works with HTML.
        const formattedDraftBody = draftResponse.trim().replace(/\n/g, '<br>');

        await mailClient.draftReply({
          threadItemId,
          draftBody: formattedDraftBody,
        });

        handleTerminatingAction();

        // only remove suggestion from list at the very end of a successful draft creation
        if (clickedSuggestion) {
          setCurrentState((prevState) => ({
            ...prevState,
            clickedSuggestion: null,
            curSuggestionList: prevState.curSuggestionList.filter(
              (s) => s.id !== clickedSuggestion.id
            ),
          }));
        }

        // When in Read View, there's a lag between when Outlook created the draft in the backend
        // and showing it in the client so wait for a moment before showing toaster.
        setTimeout(
          () => {
            toast({
              description: 'Successfully created draft reply.',
              variant: 'success',
            });

            setLoadingDraftReply(false);
          },
          isOutlookReply ? 1 : 3500
        );
      } catch (error: unknown) {
        captureException(error, { functionName: 'handleDraftResponse' });

        toast({
          description: 'Something went wrong creating draft reply',
          variant: 'destructive',
        });

        setLoadingDraftReply(false);
      }
    };
  }

  // Reset scroll position and expanded state when quote changes
  useEffect(() => {
    if (quoteResponse?.stops.length === 2 && stopsContainerRef.current) {
      stopsContainerRef.current.scrollLeft = 0;
    }
    // Reset expanded state when getting new quote
    setStopsExpanded(false);
    setCurrentStopIndex(0);
  }, [quoteResponse?.stops.length]);

  // Carousel navigation functions for route stops
  const scrollToStop = (index: number) => {
    if (!stopsContainerRef.current || !quoteResponse) return;
    if (index < 0 || index >= quoteResponse.stops.length) return;

    const container = stopsContainerRef.current;
    // Find all stop span elements (not arrows)
    // Select only the stop labels we render (exclude arrow icons/containers)
    const stopElements = container.querySelectorAll('.qq-stop-label');

    if (stopElements[index]) {
      const targetElement = stopElements[index] as HTMLElement;
      const containerRect = container.getBoundingClientRect();
      const targetRect = targetElement.getBoundingClientRect();

      // Calculate the scroll position to center the target element
      const scrollLeft =
        container.scrollLeft +
        (targetRect.left - containerRect.left) -
        containerRect.width / 2 +
        targetRect.width / 2;

      // Smooth scroll to the calculated position
      container.scrollTo({
        left: scrollLeft,
        behavior: 'smooth',
      });

      setCurrentStopIndex(index);
    }
  };

  const scrollLeft = () => scrollToStop(Math.max(0, currentStopIndex - 1));
  const scrollRight = () =>
    scrollToStop(
      Math.min((quoteResponse?.stops.length || 1) - 1, currentStopIndex + 1)
    );

  const canScrollLeft = currentStopIndex > 0;
  const canScrollRight =
    quoteResponse && currentStopIndex < quoteResponse.stops.length - 1;

  // Submit to Portal button, only if on an integrated portal
  // `quote` param may be total sell or line haul quote, depending on which button is clicked
  const handleSubmitToPortal = async (quote: Maybe<number>) => {
    if (!portal) {
      captureException(
        'Submit to Portal button clicked but not on a supported portal',
        {
          functionName: 'handleSubmitToPortal',
        }
      );

      toast({
        description: 'Not on a supported portal.',
        variant: 'warning',
      });
      return;
    }

    if (
      // NOTE: Button disabled attribute not used because webpage may change without sidepanel change
      !portal.canSubmit(await getCurrentTab(), (await getTabHTML(tabId)) ?? '')
    ) {
      toast({
        description:
          'Submission is not supported for this page. Please navigate to submission page.',
        variant: 'warning',
      });
      return;
    }

    if (!quote) {
      toast({
        description: 'No quote to submit.',
        variant: 'info',
      });
      return;
    }

    setIsSubmittingToPortal(true);

    handleTerminatingAction();
    try {
      const message: SubmitQuoteToPortalMessage = {
        targetTabId: tabId,
        action: portal.submitAction,
        data: {
          distance: maxDistance,
          flatRate: _.round(quote),
          isSubmitOnPortalEnabled:
            quickQuoteConfig?.isSubmitOnPortalEnabled || false,
        },
      };

      const sendMessage = () =>
        new Promise((resolve, reject) => {
          try {
            chrome.runtime.sendMessage(message, (response) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else if (response && response.success) {
                resolve(response);
              } else {
                reject(response);
              }
            });
          } catch (err) {
            reject(err);
          }
        });

      await sendMessage();
      const toastMsg = quickQuoteConfig?.isSubmitOnPortalEnabled
        ? 'Successfully submitted quote to portal'
        : 'Filled out quote form. Please review and submit';
      toast({
        description: toastMsg,
        variant: 'success',
      });
    } catch (error: any) {
      captureException(error, { functionName: 'handleSubmitToPortal' });

      if (error?.partialSuccess) {
        toast({
          description:
            'Entered quote in the portal, but could not submit. Please submit manually.',
          variant: 'warning',
        });
      } else {
        toast({
          description: 'Failed to submit quote to portal.',
          variant: 'destructive',
        });
      }
    } finally {
      setIsSubmittingToPortal(false);
    }
  };

  return (
    <div>
      <ExtendedFormProvider aiDefaultValues={true}>
        <FormProvider {...formMethods}>
          <TooltipProvider>
            <form
              onSubmit={handleSubmit(onSubmitForm)}
              className='grid gap-5 grid-cols-1 mx-0 mb-4 w-full'
            >
              <Grid gap='md' className='w-full mx-0'>
                <Typography
                  variant='h5'
                  className='text-neutral-800 whitespace-nowrap'
                >
                  Quote Information
                </Typography>

                {tmsIntegrations &&
                  tmsIntegrations.length > 0 &&
                  isCustomerSupportedTMS(tmsIntegrations) && (
                    <div>
                      <RHFDebounceSelect
                        required={false}
                        name='customerName'
                        label='Customer'
                        control={control}
                        errors={errors}
                        data={customers}
                        isLoading={customersLoading}
                        showSearchParamDropdown={false}
                        refreshHandler={handleRefreshCustomers}
                        resetOptionsHandler={handleResetCustomerSearch}
                        fetchOptions={handleCustomerSearch}
                        mapOptions={mapCustomerToAntdOptions}
                      />
                    </div>
                  )}

                <div>
                  <Label name={'transportType'} required={true}>
                    Transport Type
                  </Label>
                  <Controller
                    name='transportType'
                    control={control}
                    rules={{ required: 'Required' }}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1'>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent>
                          {transportTypeOptions.map(
                            (option: TransportTypeOption) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  <ErrorMessage
                    errors={errors}
                    name={'transportType'}
                    render={({ message }: { message: string }) => (
                      <Typography variant='body-xs' className='text-error-500'>
                        {message}
                      </Typography>
                    )}
                  />
                </div>
              </Grid>

              {/* Stops Inputs */}

              {isMultiStopQuickQuoteEnabled && (
                <div className='flex flex-col gap-2 w-full mx-0'>
                  {/* Pickup */}
                  <div className='grid gap-1 grid-cols-1 w-full mx-0'>
                    <div className='text-md text-neutral-700 font-semibold'>
                      Pickup
                    </div>

                    <div className='grid grid-cols-5 gap-1.5 w-full mx-0'>
                      <div className='col-span-3'>
                        <QuickQuoteTextInput
                          name={`stops.0.location`}
                          label='Location'
                          placeholder='Zip or City, State'
                          required
                        />
                      </div>

                      <div className='flex flex-col w-full col-span-2'>
                        <Label name='pickupDate' required={false}>
                          Date
                          <span className='ml-1 text-xs text-neutral-600'>
                            (optional)
                          </span>
                        </Label>
                        <Controller
                          name={`pickupDate`}
                          control={control}
                          render={({ field }) => (
                            <div className='mt-1 flex flex-row'>
                              <DatePicker
                                field={field}
                                className='pl-2'
                                calendarClassName='mr-1.5'
                              />
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Middle stops */}
                  {fields.slice(1, -1).map((field, index) => (
                    <div
                      key={field.id}
                      className='grid gap-1.5 grid-cols-1 w-full mx-0'
                    >
                      <div className='flex justify-between items-center'>
                        <div className='text-md text-neutral-700 font-semibold'>
                          Stop {index + 1}
                        </div>
                        <button
                          title='Remove stop'
                          onClick={() => remove(index + 1)}
                          className='flex items-center justify-center rounded hover:text-destructive transition-colors bg-transparent m-0 p-0 min-h-0 min-w-0 h-auto w-auto'
                          type='button'
                        >
                          <XCircleIcon className='w-4 h-4' />
                        </button>
                      </div>

                      <div className='col-span-3'>
                        <QuickQuoteTextInput
                          name={`stops.${index + 1}.location`}
                          label='Location'
                          placeholder='Zip or City, State'
                          required
                        />
                      </div>
                    </div>
                  ))}

                  {/* Add Stop button */}
                  <div className='flex justify-center mt-1'>
                    <Tooltip delayDuration={300}>
                      <TooltipTrigger asChild>
                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.QuickQuoteAddStop
                          }
                          type='button'
                          variant='ghost'
                          onClick={handleAddStop}
                          className='w-fit text-xs text-neutral-800 pl-1.5 hover:border-neutral-700 hover:bg-neutral-100 h-8 px-2.5'
                          size='sm'
                        >
                          <Plus className='h-4 w-4 mr-1' />
                          Add Stop
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent
                        className='max-w-48 items-center justify-center'
                        side='top'
                        align='center'
                      >
                        <div className='flex flex-row items-center gap-2.5'>
                          <BetaBadge />
                          <Typography variant='body-xs' weight='medium'>
                            Multi-stop quoting is in beta - try it out!
                          </Typography>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </div>

                  {/* Dropoff */}
                  <div className='grid gap-1 grid-cols-1 w-full mx-0'>
                    <div className='text-md text-neutral-700 font-semibold'>
                      Dropoff
                    </div>

                    <div className='grid grid-cols-5 gap-1.5 w-full mx-0'>
                      <div className='col-span-3'>
                        <QuickQuoteTextInput
                          name={`stops.${fields.length - 1}.location`}
                          label='Location'
                          placeholder='Zip or City, State'
                          required
                        />
                      </div>

                      <div className='flex flex-col w-full col-span-2'>
                        <Label name='deliveryDate' required={false}>
                          Date
                          <span className='ml-1 text-xs text-neutral-600'>
                            (optional)
                          </span>
                        </Label>
                        <Controller
                          name={`deliveryDate`}
                          control={control}
                          render={({ field }) => (
                            <div className='mt-1 flex flex-row'>
                              <DatePicker
                                field={field}
                                className='pl-2'
                                calendarClassName='mr-1.5'
                              />
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!isMultiStopQuickQuoteEnabled && (
                <>
                  <div className='flex justify-between items-start gap-2 w-full mx-0'>
                    {/* Pickup */}
                    <div className='w-full'>
                      <QuickQuoteTextInput
                        name='stops.0.location'
                        label='Pickup'
                        placeholder='ZIP or City, State'
                        inputClassName='placeholder:text-[12px]'
                        required
                      />
                    </div>

                    <ArrowRightIcon className='w-10 text-neutral-500 mt-7' />

                    {/* Dropoff */}
                    <div className='w-full'>
                      <QuickQuoteTextInput
                        name='stops.1.location'
                        label='Dropoff'
                        placeholder='ZIP or City, State'
                        inputClassName='placeholder:text-[12px]'
                        required
                      />
                    </div>
                  </div>

                  {hasAddedQuoteDates && (
                    <div className='flex flex-col gap-3'>
                      <p className='text-md font-semibold'>Dates</p>
                      <div className='flex justify-between items-end gap-4 w-full mx-0'>
                        <div className='w-full'>
                          <Label name='pickupDate'>Pickup</Label>
                          <Controller
                            name={`pickupDate`}
                            control={control}
                            render={({ field }) => (
                              <div className='mt-1 flex flex-row gap-1'>
                                <div className='grid grid-cols-2 gap-1 flex-1 mx-0 w-full'>
                                  <div className='col-span-2'>
                                    <DatePicker field={field} />
                                  </div>
                                </div>
                              </div>
                            )}
                          />
                        </div>

                        <div className='w-full'>
                          <Label name='deliveryDate'>Dropoff</Label>
                          <Controller
                            name={`deliveryDate`}
                            control={control}
                            render={({ field }) => (
                              <div className='mt-1 flex flex-row gap-1'>
                                <div className='grid grid-cols-2 gap-1 flex-1 mx-0 w-full'>
                                  <div className='col-span-2'>
                                    <DatePicker field={field} />
                                  </div>
                                </div>
                              </div>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {!hasAddedQuoteDates && (
                    <AddDateDetailsButton
                      buttonNamePosthog={ButtonNamePosthog.AddQuoteDateDetails}
                      onClick={() => setHasAddedQuoteDates(true)}
                    />
                  )}
                </>
              )}

              {errors.root && (
                <Flex justify='center'>
                  <div className='w-auto text-sm border-error-500 bg-error-500 text-neutral-50 rounded-lg p-2 text-center'>
                    {errors.root.message}
                  </div>
                </Flex>
              )}

              {serviceUsesDAT &&
                hasGrantedDATPermissions !== null &&
                !hasGrantedDATPermissions && (
                  <Grid
                    gap='md'
                    className='w-full mx-0 bg-info-200 border border-info-main rounded-[4px] py-4 px-3'
                  >
                    <Flex align='center' justify='between'>
                      <DATLogo height={12} />
                      <Tooltip delayDuration={10}>
                        <TooltipTrigger>
                          <InfoCircleIcon className='w-5 h-5 text-info-main cursor-pointer' />
                        </TooltipTrigger>
                        <TooltipContent>
                          Enable DAT using your email address
                        </TooltipContent>
                      </Tooltip>
                    </Flex>

                    <Flex
                      justify='between'
                      align='end'
                      gap='md'
                      className='w-full'
                    >
                      <Flex direction='col' className='w-full grow-1'>
                        <label className='text-sm text-neutral-800 mb-1'>
                          Email Address
                        </label>
                        <Input
                          name='datEmailAddress'
                          placeholder='<EMAIL>'
                          className='outline-none!'
                          onChange={(e) => setDATEmailAddress(e.target.value)}
                        />
                      </Flex>

                      <Button
                        buttonNamePosthog={ButtonNamePosthog.EnableDATForUser}
                        className='w-20 h-8 text-sm mb-px bg-brand-main/80'
                        onClick={handleEnableDATAccess}
                        disabled={isLoadingDAT || !datEmailAddress}
                        type='button'
                      >
                        {isLoadingDAT ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.EnableDATForUser
                        )}
                      </Button>
                    </Flex>
                  </Grid>
                )}

              <Button
                buttonNamePosthog={ButtonNamePosthog.GetQuickQuote}
                logProperties={getQuickQuoteProperties}
                type='submit'
                className='w-full'
                disabled={isSubmitting}
              >
                {isSubmitting ? <ButtonLoader /> : ButtonText.GetQuickQuote}
              </Button>

              {quoteResponse && !quoteNotConfident && (
                <Flex
                  direction='col'
                  gap='sm'
                  align='center'
                  className='w-full pb-2'
                >
                  <div className='w-full h-0.5 bg-neutral-300 my-2' />

                  <div className='relative w-full'>
                    {/* Stops container with collapse/expand functionality */}
                    {quoteResponse.stops.length <= 2 ? (
                      // For 2 or fewer stops, always show expanded view
                      <Flex
                        direction='row'
                        gap='sm'
                        align='center'
                        justify='center'
                      >
                        {quoteResponse.stops.map((stop, index) => {
                          const leg = routeDetails?.legs?.find(
                            (l) => l.startStopIndex === stop.order
                          );
                          return (
                            <React.Fragment key={index}>
                              <Typography
                                className={cn(
                                  'whitespace-nowrap',
                                  quoteResponse.stops.length === 2 && 'text-sm'
                                )}
                                variant='body-xs'
                                weight='medium'
                              >
                                {useHelperFunctions.toTitleCase(stop.city)},
                                {quoteResponse.stops.length > 2 && <br />}
                                {quoteResponse.stops.length === 2 && ' '}
                                {stop.state}
                              </Typography>
                              {index < quoteResponse.stops.length - 1 && (
                                <div className='flex flex-col items-center shrink-0'>
                                  {quoteResponse.stops.length > 2 &&
                                    leg &&
                                    leg.distanceMiles !== undefined && (
                                      <Typography
                                        variant='body-xxs'
                                        className='text-neutral-500 mb-[-2px] text-center whitespace-nowrap'
                                      >
                                        {`${_.round(leg.distanceMiles).toLocaleString()} mi`}
                                      </Typography>
                                    )}
                                  <ArrowRightIcon
                                    className={cn(
                                      'w-3 h-3 text-neutral-500',
                                      quoteResponse.stops.length === 2
                                        ? 'w-4 h-4'
                                        : ''
                                    )}
                                  />
                                </div>
                              )}
                            </React.Fragment>
                          );
                        })}
                      </Flex>
                    ) : (
                      // For 3+ stops, show collapsed or expanded view
                      <>
                        {!stopsExpanded ? (
                          // Collapsed view: first stop → count → last stop
                          <Tooltip delayDuration={100}>
                            <TooltipTrigger asChild>
                              <div
                                className='flex gap-2 items-center mx-auto w-max max-w-full cursor-pointer hover:bg-neutral-50 rounded p-1 transition-colors'
                                onClick={() => setStopsExpanded(true)}
                              >
                                <Typography
                                  variant='body-xs'
                                  className='whitespace-nowrap'
                                  weight='medium'
                                >
                                  {useHelperFunctions.toTitleCase(
                                    quoteResponse.stops[0].city
                                  )}
                                  ,
                                  <br />
                                  {quoteResponse.stops[0].state}
                                </Typography>
                                <div className='flex flex-col items-center shrink-0'>
                                  <Typography
                                    variant='body-xxs'
                                    className='text-neutral-600 mb-1 text-center whitespace-nowrap font-medium rounded-full px-1.5 py-0.5 cursor-pointer border-transparent hover:border-brand-500 hover:text-brand-500  hover:scale-105 transition-all duration-100 '
                                  >
                                    {`+${quoteResponse.stops.length - 2} stop${
                                      quoteResponse.stops.length - 2 === 1
                                        ? ''
                                        : 's'
                                    }`}
                                  </Typography>
                                  <ArrowRightIcon className='w-3 h-3 text-neutral-500' />
                                </div>
                                <Typography
                                  variant='body-xs'
                                  weight='medium'
                                  className='whitespace-nowrap'
                                >
                                  {useHelperFunctions.toTitleCase(
                                    quoteResponse.stops[
                                      quoteResponse.stops.length - 1
                                    ].city
                                  )}
                                  ,
                                  <br />
                                  {
                                    quoteResponse.stops[
                                      quoteResponse.stops.length - 1
                                    ].state
                                  }
                                </Typography>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Expand stops</p>
                            </TooltipContent>
                          </Tooltip>
                        ) : (
                          // Expanded view with carousel controls
                          <div className='flex flex-col gap-2'>
                            <div className='flex items-center justify-center w-full'>
                              {/* Left navigation button */}
                              <div className='shrink-0 mr-2'>
                                <button
                                  onClick={scrollLeft}
                                  disabled={!canScrollLeft}
                                  type='button'
                                  className={cn(
                                    'p-1 rounded-full transition-colors',
                                    canScrollLeft
                                      ? 'hover:bg-neutral-100 text-neutral-500'
                                      : 'text-neutral-300 cursor-not-allowed'
                                  )}
                                  aria-label='Scroll left'
                                >
                                  <ChevronLeftIcon className='w-4 h-4' />
                                </button>
                              </div>

                              {/* Stops container wrapper */}
                              <div className='flex-1 overflow-hidden px-2'>
                                <div
                                  ref={stopsContainerRef}
                                  className='flex gap-2 overflow-x-auto items-start w-full [&::-webkit-scrollbar]:hidden'
                                >
                                  {quoteResponse.stops.map((stop, index) => {
                                    const leg = routeDetails?.legs?.find(
                                      (l) => l.startStopIndex === stop.order
                                    );
                                    return (
                                      <React.Fragment key={index}>
                                        <Typography
                                          variant='body-xs'
                                          className='whitespace-nowrap shrink-0 qq-stop-label'
                                        >
                                          {useHelperFunctions.toTitleCase(
                                            stop.city
                                          )}
                                          ,
                                          <br />
                                          {stop.state}
                                        </Typography>
                                        {index <
                                          quoteResponse.stops.length - 1 && (
                                          <div className='flex flex-col items-center shrink-0'>
                                            {leg &&
                                              leg.distanceMiles !==
                                                undefined && (
                                                <Typography
                                                  variant='body-xxs'
                                                  className='text-neutral-600 mb-[-2px] text-center whitespace-nowrap'
                                                >
                                                  {`${_.round(leg.distanceMiles).toLocaleString()} mi`}
                                                </Typography>
                                              )}
                                            <ArrowRightIcon className='w-3 h-3 text-neutral-500' />
                                          </div>
                                        )}
                                      </React.Fragment>
                                    );
                                  })}
                                </div>
                              </div>

                              {/* Right navigation button */}
                              <div className='shrink-0 ml-2'>
                                <button
                                  onClick={scrollRight}
                                  disabled={!canScrollRight}
                                  type='button'
                                  className={cn(
                                    'p-1 rounded-full transition-colors',
                                    canScrollRight
                                      ? 'hover:bg-neutral-100 text-neutral-500'
                                      : 'text-neutral-300 cursor-not-allowed'
                                  )}
                                  aria-label='Scroll right'
                                >
                                  <ChevronRightIcon className='w-4 h-4' />
                                </button>
                              </div>
                            </div>

                            {/* Scroll hint for multiple stops */}
                            {quoteResponse.stops.length > 3 && (
                              <div className='text-xs text-neutral-500 mt-1 text-center'>
                                ← Scroll to view more stops →
                              </div>
                            )}
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  <Flex
                    direction='row'
                    gap='md'
                    className='mt-2 w-full'
                    align='center'
                    justify='center'
                  >
                    {maxDistance > 0 && (
                      <>
                        <Typography variant='body-sm'>
                          {`Est. Total Distance: ${_.round(maxDistance).toLocaleString()} miles`}
                        </Typography>

                        {/* Tooltip for distance source (GS, DAT, E2Open, FreightView, etc) */}
                        <Tooltip delayDuration={10}>
                          <TooltipTrigger asChild>
                            <InfoIcon className='h-4 w-4' />
                          </TooltipTrigger>
                          <TooltipContent>
                            {distanceSource && (
                              <Typography variant='body-xs'>
                                {`Distance from ${integrationNameMap[distanceSource]}`}
                              </Typography>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </>
                    )}

                    {/* Link to Google Map route */}
                    <Tooltip delayDuration={10}>
                      <TooltipTrigger asChild>
                        <MapIcon
                          className='cursor-pointer h-4 w-4 hover:!stroke-brand'
                          onClick={() =>
                            openExternalUrl(
                              constructGoogleMapsUrl(quoteResponse.stops)
                            )
                          }
                        />
                      </TooltipTrigger>
                      <TooltipContent>View route in Google Maps</TooltipContent>
                    </Tooltip>
                  </Flex>

                  <div className='w-full h-0.5 bg-neutral-300 my-2' />

                  {/* Display quote cards from each integration (DAT, GS, etc) */}
                  <Flex direction='col' gap='md' className='w-[95%]'>
                    {quoteCards.map((card) => (
                      <QuoteCard
                        key={card.type}
                        carrier={card}
                        isSelected={selectedQuote === card.type}
                        onClick={() => {
                          setHasUserSelectedCard(true);
                          setSelectedQuote(card.type);
                          if (carrierCostType === CarrierCostType.Flat) {
                            setCarrierCost(_.round(card.cost));
                          } else {
                            // Use costPerMile if available
                            if (card?.costPerMile) {
                              setCarrierCost(card?.costPerMile);
                            } else {
                              // Otherwise, reset carrier cost to flat
                              setCarrierCostType(CarrierCostType.Flat);
                              setCarrierCost(_.round(card.cost));
                            }
                          }
                        }}
                        lowConfidenceThreshold={
                          quoteResponse.configuration?.lowConfidenceThreshold ||
                          70
                        }
                        mediumConfidenceThreshold={
                          quoteResponse.configuration
                            ?.mediumConfidenceThreshold || 80
                        }
                        onUpdateRateView={(
                          timeframe,
                          areaType,
                          setUpdateRateViewError
                        ) =>
                          handleUpdateDATRate(
                            timeframe,
                            areaType,
                            setUpdateRateViewError
                          )
                        }
                        stops={quoteResponse.stops || []}
                      />
                    ))}
                  </Flex>

                  {/* Display errors for absent quote cards, if any */}
                  {quoteCardErrors.length > 0 && (
                    <Flex direction='col' gap='xs' className='mt-2'>
                      {quoteCardErrors.map((error, i) => {
                        const [source, message] = error.split(' - ');
                        return (
                          <Flex key={`quote-error-${i}`} gap='xs'>
                            <Typography
                              key={`quote-error-${i}`}
                              variant='body-xs'
                              className='text-error-500'
                            >
                              <b>{source}</b>
                              {` - ${message}`}
                            </Typography>
                          </Flex>
                        );
                      })}
                    </Flex>
                  )}
                </Flex>
              )}

              {/* Lane History Charts */}
              {(quoteNotConfident || quoteResponse) && (
                <>
                  <Typography
                    variant='h5'
                    weight='semibold'
                    className='text-neutral-800 whitespace-nowrap'
                  >
                    Lane History Trends
                  </Typography>

                  {/* Feature flag for DAT Lane History on demand */}
                  {hasDATRateView && isOnDemandDATLaneHistoryEnabled && (
                    <DATLaneHistory
                      quoteResponse={quoteResponse}
                      quoteCards={quoteCards}
                      transportType={getValues('transportType')}
                      DATLaneHistoryData={DATLaneHistoryData}
                      setDATLaneHistoryData={setDATLaneHistoryData}
                      hasPulledDATLaneHistory={hasPulledDATLaneHistory}
                      setHasPulledDATLaneHistory={setHasPulledDATLaneHistory}
                    />
                  )}

                  {shouldShowLaneHistoryTrends ? (
                    <LaneHistoryTrends
                      isLoadingLaneHistory={isLoadingLaneHistory}
                      laneHistory={laneHistory}
                      tmsLaneHistory={tmsLaneHistory}
                      tmsLaneHistorySelectedTierIndex={
                        tmsLaneHistorySelectedTierIndex
                      }
                      quoteResponse={quoteResponse}
                      toggleLaneHistoryGraph={toggleLaneHistoryGraph}
                    />
                  ) : null}

                  {/* Lane History from Service Chart */}
                  {isGetLaneRateFromServiceEnabled && (
                    <>
                      {isLoadingLaneHistoryFromService ? (
                        <div className='flex flex-row justify-center gap-1'>
                          <p className='text-sm text-neutral-500'>
                            Fetching DAT Rate History...
                          </p>
                          <ButtonLoader />
                        </div>
                      ) : laneHistoryFromService ? (
                        <>
                          <LaneHistoryFromServiceChart
                            months={laneHistoryFromService.months}
                            last7Days={laneHistoryFromService.last7Days}
                            title='DAT Rate History'
                            subtitle={titleCase(
                              laneHistoryFromService.equipment
                            )}
                            description='Monthly rate trends with 7-day recent data.'
                          />
                          <Typography
                            variant='body-xs'
                            className='text-neutral-600 italic'
                          >
                            Showing 2-stop data from the first and last stop in
                            the form. Multi-stop coming soon.
                          </Typography>
                        </>
                      ) : null}
                    </>
                  )}

                  <CarrierPriceCalculator
                    calculatorParent={CarrierPriceCalculatorParent.QuickQuote}
                    parentQuoteRequestId={parentQuoteRequestId}
                    showTitle={false}
                    mileage={maxDistance}
                    mileageSource={distanceSource}
                    finalPrice={finalPrice}
                    fuelEstimate={fuelEstimate}
                    datFuelSurcharge={datFuelSurcharge}
                    portalFuelSurchargeSource={
                      clickedSuggestion?.pipeline ===
                      SuggestionPipelines.QuickQuote
                        ? (clickedSuggestion?.source ?? null)
                        : null
                    }
                    portalFuelSurcharge={
                      clickedSuggestion?.pipeline ===
                      SuggestionPipelines.QuickQuote
                        ? clickedSuggestion?.suggested.fuelSurchargePerMile
                        : null
                    }
                    profit={profit}
                    setProfitHandler={setProfit}
                    profitType={profitType}
                    setProfitTypeHandler={setProfitType}
                    maxDistance={maxDistance}
                    terminatingActionHandler={handleTerminatingAction}
                    carrierCost={carrierCost}
                    carrierCostType={carrierCostType}
                    setCarrierCostTypeHandler={setCarrierCostType}
                    setCarrierCostHandler={setCarrierCost}
                    setFuelEstimateHandler={setFuelEstimate}
                    setFinalPriceHandler={setFinalPrice}
                    selectedQuickQuoteId={getSelectedQuickQuoteId()}
                    defaultFSCProvider={
                      quoteResponse && quoteResponse.configuration
                        ? quoteResponse.configuration.fscProvider
                        : ''
                    }
                    setStopFeeHandler={setStopFee}
                    stopFee={stopFee}
                    selectedQuoteType={selectedQuote}
                  />
                </>
              )}
            </form>

            {quoteResponse &&
              quoteResponse.configuration?.belowThresholdMessage && (
                <div className='w-auto text-sm border-error-500 bg-error-500 text-neutral-50 rounded-lg p-2 mb-4 text-center'>
                  {quoteResponse.configuration?.belowThresholdMessage}
                </div>
              )}

            {(quoteResponse || quoteNotConfident) && (
              <div
                className='flex-col justify-center'
                ref={scrollResultsIntoViewRef}
              >
                <Divider className='border border-neutral-400 my-6' />

                {/* Submit quote via third-party URL (e.g. shipper TMSes) */}
                {isQuoteSubmissionViaURLEnabled && hasThirdPartyQuoteURLs && (
                  <>
                    <Typography
                      variant='h5'
                      weight='semibold'
                      className='text-neutral-800 whitespace-nowrap'
                    >
                      Submission Hyperlink Detected
                    </Typography>
                    <div className='w-full mt-4'>
                      <QuickQuoteTextInput
                        name='quoteNumber'
                        placeholder='Q000001'
                        label='Quote #'
                        required
                      />
                    </div>
                    <Flex justify='between' gap='sm' className='w-full mt-2'>
                      <Flex direction='col' className='flex-1'>
                        <Label name='quoteExpirationDate'>Expiration</Label>
                        <Controller
                          name={`quoteExpirationDate`}
                          control={control}
                          render={({ field }) => (
                            <Flex gap='xs' className='mt-1'>
                              <Grid
                                cols='2'
                                gap='xs'
                                className='flex-1 mx-0 w-full'
                              >
                                <div className='col-span-2'>
                                  <DatePicker field={field} />
                                </div>
                              </Grid>
                            </Flex>
                          )}
                        />
                        {errors.quoteExpirationDate && (
                          <Typography
                            variant='body-xs'
                            className='text-error-500'
                          >
                            {errors.quoteExpirationDate.message}
                          </Typography>
                        )}
                      </Flex>
                      <Flex direction='col' className='flex-1'>
                        <Label name='Eta'>ETA</Label>
                        <Controller
                          name={`quoteEta`}
                          control={control}
                          render={({ field }) => (
                            <Flex gap='xs' className='mt-1'>
                              <Grid
                                cols='2'
                                gap='xs'
                                className='flex-1 mx-0 w-full'
                              >
                                <div className='col-span-2'>
                                  <DatePicker field={field} />
                                </div>
                              </Grid>
                            </Flex>
                          )}
                        />
                        {errors.quoteEta && (
                          <Typography
                            variant='body-xs'
                            className='text-error-500'
                          >
                            {errors.quoteEta.message}
                          </Typography>
                        )}
                      </Flex>
                    </Flex>

                    <Button
                      className='w-full h-8 text-sm mt-4'
                      type='button'
                      buttonNamePosthog={ButtonNamePosthog.SubmitQuoteViaURL}
                      disabled={loadingDraftReply}
                      onClick={() => {
                        const currentSelectedQuickQuoteIdForURL =
                          getSelectedQuickQuoteId();
                        useHelperFunctions.handleQuoteSubmissionViaURL({
                          email,
                          quoteResponse,
                          getValues,
                          setError,
                          setLoadingDraftReply,
                          finalPrice,
                          isTMSQuoteSubmissionEnabled,
                          isSubmitToTMS,
                          setCreatedQuoteId,
                          clickedSuggestion,
                          setCurrentState,
                          parentQuoteRequestId,
                          profit,
                          profitType,
                          carrierCost,
                          carrierCostType,
                          maxDistance,
                          selectedQuickQuoteId:
                            currentSelectedQuickQuoteIdForURL,
                        });
                      }}
                    >
                      {loadingDraftReply ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.SubmitQuoteViaURL
                      )}
                    </Button>
                  </>
                )}

                {/* Submit to Portal buttons if on an integrated portal */}
                {showSubmitToPortalButtons && (
                  <Flex gap='sm' align='center' className='mt-4 w-full'>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className='flex-1'>
                            <Button
                              buttonNamePosthog={
                                ButtonNamePosthog.SubmitLineHaulToPortal
                              }
                              logProperties={{
                                portal: (
                                  clickedSuggestion as SuggestedQuoteChange
                                )?.source,
                                selectedQuote: selectedQuote,
                                quoteRequestId: parentQuoteRequestId,
                              }}
                              className='w-full bg-neutral-50 text-brand-main border border-brand-300 hover:bg-brand-50'
                              type='button'
                              disabled={isSubmittingToPortal || !finalFlatPrice}
                              onClick={() =>
                                handleSubmitToPortal(
                                  finalFlatPrice
                                    ? finalFlatPrice - fuelEstimate
                                    : null
                                )
                              }
                            >
                              {isSubmittingToPortal ? (
                                <ButtonLoader />
                              ) : (
                                `${quickQuoteConfig?.isSubmitOnPortalEnabled ? 'Submit' : 'Input'} line haul`
                              )}
                            </Button>
                          </div>
                        </TooltipTrigger>
                        {!finalFlatPrice && (
                          <TooltipContent>
                            <Typography>
                              Unable to submit on this page
                            </Typography>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className='flex-1'>
                            <Button
                              buttonNamePosthog={
                                ButtonNamePosthog.SubmitTotalToPortal
                              }
                              logProperties={{
                                portal: (
                                  clickedSuggestion as SuggestedQuoteChange
                                )?.source,
                                selectedQuote: selectedQuote,
                                quoteRequestId: parentQuoteRequestId,
                              }}
                              className='w-full'
                              type='button'
                              disabled={isSubmittingToPortal || !finalFlatPrice}
                              onClick={() =>
                                handleSubmitToPortal(finalFlatPrice)
                              }
                              variant='default'
                            >
                              {isSubmittingToPortal ? (
                                <ButtonLoader />
                              ) : (
                                `${quickQuoteConfig?.isSubmitOnPortalEnabled ? 'Submit' : 'Input'} total sell`
                              )}
                            </Button>
                          </div>
                        </TooltipTrigger>
                        {!finalFlatPrice && (
                          <TooltipContent>
                            <Typography>
                              Unable to submit on this page
                            </Typography>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  </Flex>
                )}

                {/* Show draft response if Drumkit running on email platform (Gmail, Outlook, Front) */}
                {isEmailPlatform(drumkitPlatform) && (
                  <div>
                    <Typography
                      variant='h5'
                      weight='semibold'
                      className='text-neutral-800 whitespace-nowrap'
                    >
                      Draft Response
                    </Typography>

                    <div className='relative mt-3'>
                      <Button
                        buttonNamePosthog={
                          ButtonNamePosthog.CopyQuoteToClipboard
                        }
                        logProperties={copyToClipboardProperties}
                        className={cn(
                          'absolute h-6 w-6 p-0 -top-8 right-0 border-none',
                          hasCopiedDraftResponse
                            ? 'cursor-default'
                            : 'cursor-pointer'
                        )}
                        variant='ghost'
                        type='button'
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          !hasCopiedDraftResponse && handleCopyToClipboard();
                        }}
                      >
                        {hasCopiedDraftResponse ? (
                          <Tooltip open={true}>
                            <TooltipTrigger asChild>
                              <CheckIcon className='h-4 w-4' />
                            </TooltipTrigger>
                            <TooltipContent>Copied!</TooltipContent>
                          </Tooltip>
                        ) : (
                          <CopyIcon className='h-4 w-4' />
                        )}
                      </Button>

                      {/* Draft Response Textarea */}
                      <JoditEditor
                        className='mt-2'
                        config={getJoditEditorConfig({
                          refToUpdateAfterInit: joditRef,
                        })}
                        value={draftResponse}
                        onBlur={(e: any) => {
                          setDraftResponse(e);
                          !userEditedDraft && setUserEditedDraft(true);
                        }}
                      />
                    </div>

                    {/* If distance exists, allow user to toggle format of final price in response */}
                    {Boolean(maxDistance) && (
                      <Flex
                        align='center'
                        className='whitespace-nowrap min-w-0 mt-1 overflow-hidden'
                      >
                        <span className='text-[13px] xxs:text-xs text-neutral-500 mr-2'>
                          Show:
                        </span>
                        <div className='inline-flex rounded-[4px] max-h-5 border border-neutral-400 text-xs'>
                          <button
                            type='button'
                            title={'Flat Rate'}
                            onClick={() =>
                              setFinalPriceFormat(CarrierCostType.Flat)
                            }
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === CarrierCostType.Flat
                                ? 'text-brand-400 font-medium bg-brand-50'
                                : 'text-neutral-500 hover:text-brand-400'
                            }`}
                          >
                            Flat Rate
                          </button>
                          <button
                            title={'Per Mile Rate'}
                            type='button'
                            onClick={() =>
                              setFinalPriceFormat(CarrierCostType.PerMile)
                            }
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === CarrierCostType.PerMile
                                ? 'text-brand-400 font-medium bg-brand-50'
                                : 'text-neutral-500 hover:text-brand-400'
                            }`}
                          >
                            Per Mile
                          </button>
                          <button
                            title={'Both Rates'}
                            type='button'
                            onClick={() => setFinalPriceFormat('Both')}
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === 'Both'
                                ? 'text-brand-400 font-medium bg-brand-50'
                                : 'text-neutral-500 hover:text-brand-400'
                            }`}
                          >
                            Both
                          </button>
                          <button
                            title={'Customer Linehaul'}
                            type='button'
                            onClick={() =>
                              setFinalPriceFormat(CarrierCostType.Linehaul)
                            }
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === 'Linehaul'
                                ? 'text-brand-400 font-medium bg-brand-50'
                                : 'text-neutral-500 hover:text-brand-400'
                            }`}
                          >
                            Linehaul
                          </button>
                        </div>
                      </Flex>
                    )}

                    {/** Reply drafts are only supported on Outlook for now */}
                    {drumkitPlatform === DrumkitPlatform.Outlook && (
                      <Button
                        className='w-full h-8 text-sm mt-4'
                        type='button'
                        buttonNamePosthog={
                          isOutlookReply
                            ? ButtonNamePosthog.AddReplyToCurrentDraft
                            : ButtonNamePosthog.CreateDraftReply
                        }
                        disabled={loadingDraftReply}
                        onClick={() => handleDraftResponse()}
                      >
                        {isOutlookReply ? (
                          ButtonText.AddReplyToCurrentDraft
                        ) : loadingDraftReply ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.CreateDraftReply
                        )}
                      </Button>
                    )}
                  </div>
                )}

                {isTMSQuoteSubmissionEnabled && (
                  <>
                    <Divider className='border border-neutral-400 my-6' />

                    <Typography
                      variant='h5'
                      weight='semibold'
                      className='text-neutral-800 whitespace-nowrap'
                    >
                      Save quote in TMS
                    </Typography>

                    <div className='mt-4'>
                      <RHFDebounceSelect
                        required={false}
                        name='customerName'
                        label='Customer'
                        control={control}
                        errors={errors}
                        data={customers}
                        isLoading={customersLoading}
                        showSearchParamDropdown={false}
                        refreshHandler={handleRefreshCustomers}
                        resetOptionsHandler={handleResetCustomerSearch}
                        fetchOptions={handleCustomerSearch}
                        mapOptions={mapCustomerToAntdOptions}
                      />
                      {newCustomerURLInTMS && (
                        <a
                          className='underline text-[12px] text-brand'
                          target='_blank'
                          rel='noreferrer'
                          href={newCustomerURLInTMS}
                        >
                          or create a new customer.
                        </a>
                      )}
                    </div>

                    <CreatedQuoteMessage
                      createdQuoteId={createdQuoteId}
                      quoteURLInTMS={quoteURLInTMS}
                      showWithEmptyQuoteId={tmsName === TMS.ThreeG}
                    />

                    <Button
                      className='w-full h-8 text-sm mt-4'
                      type='button'
                      buttonNamePosthog={ButtonNamePosthog.SubmitQuoteToTMS}
                      disabled={submitQuoteLoading}
                      onClick={async () => await handleSubmitQuoteToTMS()}
                    >
                      {submitQuoteLoading ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.SubmitQuoteToTMS
                      )}
                    </Button>
                  </>
                )}
              </div>
            )}
          </TooltipProvider>
        </FormProvider>
      </ExtendedFormProvider>
    </div>
  );
}

interface CreatedQuoteMessageProps {
  createdQuoteId: Maybe<string>;
  quoteURLInTMS: Maybe<string>;
  showWithEmptyQuoteId?: boolean;
}

const CreatedQuoteMessage = ({
  createdQuoteId,
  quoteURLInTMS,
  showWithEmptyQuoteId = false,
}: CreatedQuoteMessageProps) => {
  const {
    currentState: { openExternalUrl },
  } = useContext(SidebarStateContext);

  const isEmptyQuoteId = createdQuoteId === '';

  return createdQuoteId || (isEmptyQuoteId && showWithEmptyQuoteId) ? (
    <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-800 px-4 bg-success-50'>
      <Typography weight='semibold' className='mb-2'>
        Quote Created 🎉
      </Typography>

      {createdQuoteId ? (
        <Typography variant='body-sm' className='mb-2'>
          <b className='text-[14px]'>Quote ID #: </b>
          {createdQuoteId}
        </Typography>
      ) : (
        <Typography variant='body-sm' className='mb-2'>
          No Quote ID was provided by the TMS to Drumkit.
        </Typography>
      )}

      {quoteURLInTMS && (
        <Typography variant='body-sm' className='mb-1'>
          <Typography
            variant='link'
            onClick={() => openExternalUrl(quoteURLInTMS)}
          >
            Access the created quote for more details
          </Typography>
        </Typography>
      )}
    </div>
  ) : null;
};
