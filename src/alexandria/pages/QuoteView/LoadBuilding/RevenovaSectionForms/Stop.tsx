import { useEffect } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getLocations } from 'lib/api/getLocations';
import { LoadDateTimeInput } from 'pages/LoadView/LoadInformation/Components';
import { NormalizedLoad, TMSLocation } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { datetimeFieldOptions } from 'utils/formValidators';
import {
  GenericCompanySearchableFields,
  locationSearchHandler,
  mapLocationsToAntdOptions,
} from 'utils/loadInfoAndBuilding';

// Only copy these fields from the selected location
const ALLOWED_FIELDS = [
  'name',
  'addressLine1',
  'addressLine2',
  'city',
  'state',
  'zipCode',
  'country',
  'contact',
  'phone',
  'email',
  'businessHours',
  'refNumber',
  'timezone',
  'instructions',
  'readyTime',
  'mustDeliver',
  'apptStartTime',
  'apptEndTime',
  'apptNote',
  'externalTMSID',
] as const;

interface StopFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
  locations: Maybe<TMSLocation[]>;
  setLocations: React.Dispatch<React.SetStateAction<Maybe<TMSLocation[]>>>;
  isLoadingLocations: boolean;
  setIsLoadingLocations: React.Dispatch<React.SetStateAction<boolean>>;
  stopType: 'pickup' | 'consignee';
  isCreateMode?: boolean;
}

export function StopForm({
  formMethods,
  locations,
  setLocations,
  isLoadingLocations,
  setIsLoadingLocations,
  stopType,
  isCreateMode = false,
}: StopFormProps) {
  const { control, watch, setValue } = formMethods;
  const { toast } = useToast();
  const { tmsIntegrations } = useServiceFeatures();
  const prefix = stopType;

  const handleLocationSearch = async (
    field: GenericCompanySearchableFields,
    searchTerm: string
  ) => {
    return locationSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      locations,
      setLocations,
      field,
      value: searchTerm,
    });
  };

  const handleRefreshLocations = async () => {
    try {
      setIsLoadingLocations(true);
      const response = await getLocations(tmsIntegrations?.[0]?.id);
      if (response.isOk()) {
        setLocations(response.value.locationList);
        toast({
          description: 'Successfully refreshed location list.',
          variant: 'success',
        });
      } else {
        toast({
          description: response.error.message || 'Failed to refresh locations',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to refresh locations',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingLocations(false);
    }
  };

  const watchedLocationID = watch(`${prefix}.externalTMSID`);
  const watchedLocObj = watch(prefix);

  useEffect(() => {
    if (watchedLocationID) {
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) return;
      ALLOWED_FIELDS.forEach((key) => {
        if (key in selectedLoc) {
          // In create mode, don't overwrite contact if user has already entered one
          // This allows users to edit the contact even after selecting a location
          if (key === 'contact' && isCreateMode) {
            const currentValue = formMethods.getValues(
              `${prefix}.contact` as FieldPath<NormalizedLoad>
            );
            // Only set contact from location if current value is empty
            if (
              !currentValue ||
              (typeof currentValue === 'string' &&
                currentValue.trim().length === 0)
            ) {
              setValue(
                `${prefix}.${key}` as FieldPath<NormalizedLoad>,
                (selectedLoc as any)[key],
                { shouldDirty: true }
              );
            }
          } else {
            setValue(
              `${prefix}.${key}` as FieldPath<NormalizedLoad>,
              (selectedLoc as any)[key],
              { shouldDirty: true }
            );
          }
        }
      });
    }
  }, [
    watchedLocationID,
    locations,
    setValue,
    prefix,
    isCreateMode,
    formMethods,
  ]);

  useEffect(() => {
    if (watchedLocationID) {
      // Remove ID if address is write-in
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );

      if (!selectedLoc) {
        return;
      }

      // Fields to check for differences (case-insensitive comparison)
      const fieldsToCheck: Array<
        'name' | 'addressLine1' | 'addressLine2' | 'city' | 'state' | 'zipCode'
      > = ['name', 'addressLine1', 'addressLine2', 'city', 'state', 'zipCode'];

      const hasFieldDifferences = fieldsToCheck.some((field) => {
        const selectedValue = selectedLoc[field];
        const watchedValue = watchedLocObj?.[field];
        return (
          selectedValue &&
          watchedValue?.toLowerCase() !== selectedValue?.toLowerCase()
        );
      });

      if (hasFieldDifferences) {
        setValue(`${prefix}.externalTMSID`, '');
      }
    }
  }, [
    watchedLocationID,
    watchedLocObj?.name,
    watchedLocObj?.addressLine1,
    watchedLocObj?.addressLine2,
    watchedLocObj?.city,
    watchedLocObj?.state,
    watchedLocObj?.zipCode,
    locations,
    setValue,
    prefix,
  ]);

  // In edit mode, create virtual location option when:
  // 1. externalTMSID is empty but we have location data, OR
  // 2. externalTMSID exists but no matching location found in the list
  useEffect(() => {
    if (!isCreateMode && watchedLocObj && watchedLocObj.name) {
      const hasValidLocation =
        watchedLocationID &&
        locations?.find((l) => l.externalTMSID === watchedLocationID);

      if (!hasValidLocation) {
        const virtualLocation: TMSLocation = {
          ID: null,
          externalTMSID: watchedLocationID || '', // Use existing ID or empty
          name: watchedLocObj.name,
          addressLine1: watchedLocObj.addressLine1 || '',
          addressLine2: watchedLocObj.addressLine2 || '',
          city: watchedLocObj.city || '',
          state: watchedLocObj.state || '',
          zipCode: watchedLocObj.zipCode || '',
          country: watchedLocObj.country || '',
          contact: watchedLocObj.contact || '',
          phone: watchedLocObj.phone || '',
          email: watchedLocObj.email || '',
          refNumber: watchedLocObj.refNumber || '',
          apptRequired: false,
        };

        // Add virtual location to the list if it doesn't exist
        const isVirtualLocation = locations?.find(
          (l) =>
            l.name === virtualLocation.name &&
            l.externalTMSID === virtualLocation.externalTMSID
        );

        if (locations && !isVirtualLocation) {
          setLocations([virtualLocation, ...locations]);
        }
      }
    }
  }, [isCreateMode, watchedLocObj, watchedLocationID, locations, setLocations]);

  return (
    <>
      <RHFDebounceSelect
        name={`${prefix}.externalTMSID`}
        label='Name'
        control={control}
        errors={formMethods.formState.errors}
        data={locations}
        fetchOptions={handleLocationSearch}
        mapOptions={mapLocationsToAntdOptions}
        refreshHandler={handleRefreshLocations}
        isLoading={isLoadingLocations}
        required={isCreateMode}
        placeholder='Search by name or address'
        showSearchParamDropdown={false}
        searchFieldDefault='nameAddress'
      />

      <RHFTextInput
        name={`${prefix}.addressLine1`}
        label='Address Line 1'
        readOnly={true}
      />
      <RHFTextInput
        name={`${prefix}.addressLine2`}
        label='Address Line 2'
        readOnly={true}
      />
      <RHFTextInput name={`${prefix}.city`} label='City' readOnly={true} />
      <RHFTextInput name={`${prefix}.state`} label='State' readOnly={true} />
      <RHFTextInput
        name={`${prefix}.zipCode`}
        label='Zip Code'
        readOnly={true}
      />
      <RHFTextInput
        name={`${prefix}.country`}
        label='Country'
        readOnly={true}
      />
      <RHFTextInput
        name={`${prefix}.contact`}
        label='Contact Name'
        placeholder='First Last'
        required={isCreateMode}
        readOnly={!isCreateMode}
        options={
          isCreateMode
            ? {
                required: 'Contact name is required',
                validate: (value: string) => {
                  if (!value || value.trim().length === 0) {
                    return 'Contact name is required';
                  }
                  // Require at least two words (first and last name) for proper LastName parsing
                  const parts = value
                    .trim()
                    .split(/\s+/)
                    .filter((p) => p.length > 0);
                  if (parts.length < 2) {
                    return 'Please enter both first and last name (e.g., "John Smith")';
                  }
                  return true;
                },
              }
            : undefined
        }
      />
      <RHFTextInput name={`${prefix}.phone`} label='Phone' readOnly={true} />
      <RHFTextInput name={`${prefix}.email`} label='Email' readOnly={true} />
      <RHFTextInput
        name={`${prefix}.businessHours`}
        label='Business Hours'
        readOnly={true}
      />
      <RHFTextInput
        name={`${prefix}.refNumber`}
        label='Reference Number'
        readOnly={true}
      />

      {/* Date fields - these map to ExpectedDate in Salesforce */}
      {stopType === 'pickup' && (
        <LoadDateTimeInput
          name={`${prefix}.readyTime` as FieldPath<NormalizedLoad>}
          label='Pickup Date'
          options={datetimeFieldOptions}
          useUTCWhenNoTimezone
        />
      )}
      {stopType === 'consignee' && (
        <LoadDateTimeInput
          name={`${prefix}.mustDeliver` as FieldPath<NormalizedLoad>}
          label='Delivery Date'
          options={datetimeFieldOptions}
          useUTCWhenNoTimezone
        />
      )}

      {/* Appointment time fields - these map to AppointmentTime in Salesforce */}
      <LoadDateTimeInput
        name={`${prefix}.apptStartTime`}
        label='Appointment Start Time'
        options={datetimeFieldOptions}
        useUTCWhenNoTimezone
      />

      <LoadDateTimeInput
        name={`${prefix}.apptEndTime`}
        label='Appointment End Time'
        options={datetimeFieldOptions}
        useUTCWhenNoTimezone
      />

      <RHFTextInput name={`${prefix}.apptNote`} label='Appointment Note' />
      <RHFTextInput
        name={`${prefix}.timezone`}
        label='Timezone'
        readOnly={true}
      />
      <RHFTextInput
        name={`${prefix}.instructions`}
        label='Instructions'
        readOnly={true}
      />
    </>
  );
}

export default StopForm;
