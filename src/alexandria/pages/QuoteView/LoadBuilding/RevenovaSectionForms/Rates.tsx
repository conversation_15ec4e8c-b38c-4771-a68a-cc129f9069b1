import { useEffect } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import FormInputWrapper from 'components/input/FormInputWrapper';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Grid } from 'components/layout';
import { NormalizedLoad } from 'types/Load';

import { currencyList } from '../TurvoSectionForms/constants';

interface RatesFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
}

export function RatesForm({ formMethods }: RatesFormProps) {
  const { control, watch, setValue } = formMethods;

  const watchedTotalAmount = watch('rateData.customerTotalCharge.val');
  const watchedCustomerLineHaulCharge = watch(
    'rateData.customerLineHaulCharge.val'
  );
  const watchedCustomerFuelSurcharge = watch('rateData.fscFlatRate');

  // Auto-calculate total: Line Haul Charge + Fuel Surcharge
  useEffect(() => {
    const lineHaul = watchedCustomerLineHaulCharge || 0;
    const fuelSurcharge = watchedCustomerFuelSurcharge || 0;
    const calculatedTotal = lineHaul + fuelSurcharge;

    if (calculatedTotal !== watchedTotalAmount) {
      setValue('rateData.customerTotalCharge.val', calculatedTotal, {
        shouldDirty: true,
        shouldValidate: true,
      });
    }
  }, [
    watchedCustomerLineHaulCharge,
    watchedCustomerFuelSurcharge,
    watchedTotalAmount,
    setValue,
  ]);

  return (
    <Grid cols='2' gap='sm' width='full' className='mx-0'>
      <Grid
        cols='2'
        gap='sm'
        width='full'
        align='stretch'
        className='col-span-2 [&>label]:h-full [&>label]:justify-end'
      >
        <RHFTextInput
          name='rateData.customerLineHaulCharge.val'
          label='Line Haul Charge'
          options={{ valueAsNumber: true }}
        />

        <RHFTextInput
          name='rateData.fscFlatRate'
          label='Fuel Surcharge'
          options={{ valueAsNumber: true }}
        />
      </Grid>

      <Grid
        cols='2'
        gap='sm'
        width='full'
        align='stretch'
        className='col-span-2 [&>label]:h-full [&>label]:justify-end'
      >
        <RHFTextInput
          name='rateData.customerTotalCharge.val'
          label='Total Amount'
          options={{ valueAsNumber: true }}
        />

        <FormInputWrapper
          name='rateData.customerTotalCharge.unit'
          label='Currency'
          required={
            watchedTotalAmount > 0 ||
            watchedCustomerLineHaulCharge > 0 ||
            (watchedCustomerFuelSurcharge ?? 0) > 0
          }
        >
          <Controller
            name='rateData.customerTotalCharge.unit'
            control={control}
            rules={{
              required:
                watchedTotalAmount > 0 ||
                watchedCustomerLineHaulCharge > 0 ||
                (watchedCustomerFuelSurcharge ?? 0) > 0,
            }}
            render={({ field }) => (
              <AntdSelect
                showSearch={currencyList && currencyList.length > 1}
                className='h-8 text-grayscale-content-input'
                placeholder='Choose'
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={currencyList?.map((currency) => ({
                  value: currency,
                  label: currency,
                }))}
              />
            )}
          />
        </FormInputWrapper>
      </Grid>
    </Grid>
  );
}

export default RatesForm;
